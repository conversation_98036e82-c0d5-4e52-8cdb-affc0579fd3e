"""
Main Analyzer Module for 3D Pile Volume Analysis

This module orchestrates the entire pile volume analysis workflow,
providing the main API for users to perform complete analysis.
It follows Python best practices with comprehensive type hints, error handling,
logging, and validation.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
from pathlib import Path
from datetime import datetime

from .data_processor import DataProcessor
from .geometry_engine import GeometryEngine, PileGeometry
from .boundary_clipper import BoundaryClipper
from .volume_calculator import VolumeCalculator, VolumeResult
from .overlap_analyzer import OverlapAnalyzer, OverlapResult
from .report_generator import ReportGenerator
from .visualizer import Visualizer

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class AnalysisResults:
    """
    Container for complete analysis results.

    This class holds all the results from a pile volume analysis workflow,
    including geometries, volume calculations, overlap analysis, and reports.

    Attributes:
        pile_data: Original pile data from input file
        geometries: Generated pile geometries before clipping
        clipped_geometries: Pile geometries after site boundary clipping
        volume_results: Volume calculation results for each pile
        overlap_results: Overlap analysis results between piles
        overlap_volumes: Dictionary of overlap volumes between pile pairs
        individual_report: DataFrame with individual pile volume results
        overlap_report: DataFrame with overlap analysis results
        summary_report: Dictionary with summary statistics
        boundary_info: Information about site boundary processing
    """

    def __init__(self) -> None:
        """Initialize empty analysis results container."""
        self.pile_data: Optional[pd.DataFrame] = None
        self.geometries: Dict[str, PileGeometry] = {}
        self.clipped_geometries: Dict[str, PileGeometry] = {}
        self.volume_results: Dict[str, VolumeResult] = {}
        self.overlap_results: List[OverlapResult] = []
        self.overlap_volumes: Dict[Tuple[str, str], float] = {}
        self.individual_report: Optional[pd.DataFrame] = None
        self.overlap_report: Optional[pd.DataFrame] = None
        self.summary_report: Dict[str, Any] = {}
        self.boundary_info: Dict[str, Any] = {}


class MainAnalyzer:
    """
    Main orchestrator for 3D pile volume analysis workflow.

    This class provides the primary API for performing complete pile volume analysis,
    including data processing, geometry generation, volume calculation, overlap analysis,
    and report generation with CAD file export.

    Attributes:
        mesh_resolution: Resolution for 3D mesh generation
        spatial_tolerance: Tolerance for spatial operations
        enable_visualization: Whether to enable visualization features
    """

    def __init__(self,
                 mesh_resolution: int = 32,
                 spatial_tolerance: float = 0.1,
                 enable_visualization: bool = True) -> None:
        """
        Initialize the MainAnalyzer.
        
        Args:
            mesh_resolution: Resolution for 3D mesh generation
            spatial_tolerance: Tolerance for spatial operations
            enable_visualization: Whether to enable visualization capabilities
        """
        self.mesh_resolution = mesh_resolution
        self.spatial_tolerance = spatial_tolerance
        self.enable_visualization = enable_visualization
        
        # Initialize components
        self.data_processor = DataProcessor()
        self.geometry_engine = GeometryEngine(mesh_resolution=mesh_resolution)
        self.volume_calculator = VolumeCalculator()
        self.overlap_analyzer = OverlapAnalyzer(spatial_tolerance=spatial_tolerance)
        self.report_generator = ReportGenerator()
        
        if enable_visualization:
            try:
                self.visualizer = Visualizer()
            except ImportError:
                logger.warning("PyVista not available, visualization disabled")
                self.visualizer = None
        else:
            self.visualizer = None
            
        logger.info("MainAnalyzer initialized successfully")
        
    def analyze_pile_volumes(self,
                           excel_file: Union[str, Path],
                           site_boundary_coords: Optional[List[Tuple[float, float]]] = None,
                           file_paths: Optional[Dict[str, str]] = None,
                           output_dir: Union[str, Path] = "output",
                           export_reports: bool = True,
                           create_visualizations: bool = True) -> AnalysisResults:
        """
        Perform complete pile volume analysis.

        Args:
            excel_file: Path to Excel file with pile data
            site_boundary_coords: Optional list of (x, y) coordinates defining site boundary.
                                If None, will read from SiteBoundary sheet in Excel file.
            file_paths: Optional file paths for data reading
            output_dir: Directory for output files
            export_reports: Whether to export CSV reports
            create_visualizations: Whether to create visualizations

        Returns:
            AnalysisResults object with complete analysis
        """
        logger.info("Starting pile volume analysis")
        results = AnalysisResults()

        try:
            # Step 1: Read and process pile data
            logger.info("Step 1: Reading pile data")
            pile_data = self.data_processor.read_pile_data(excel_file)
            pile_data = self.data_processor.calculate_pile_properties(pile_data)
            results.pile_data = pile_data

            logger.info(f"Processed {len(pile_data)} piles")

            # Step 1.5: Read site boundary data from Excel (if not provided)
            if site_boundary_coords is None:
                logger.info("Step 1.5: Reading site boundary from Excel")
                site_boundary_coords = self.data_processor.read_site_boundary(excel_file)
                logger.info(f"Loaded site boundary with {len(site_boundary_coords)} points")
            else:
                logger.info("Using provided site boundary coordinates")

            # Quality Assurance Step 1: Validate input data
            logger.info("QA Step 1: Validating input data")
            self._validate_input_data(pile_data, site_boundary_coords)
            
            # Step 2: Create 3D geometries
            logger.info("Step 2: Creating 3D geometries")
            geometries = self.geometry_engine.create_all_pile_geometries(pile_data)
            results.geometries = geometries

            # Quality Assurance Step 2: Validate geometry creation
            logger.info("QA Step 2: Validating geometry creation")
            self._validate_geometries(geometries)

            # Step 3: Set up boundary clipping
            logger.info("Step 3: Setting up boundary clipping")
            elevation_range = self._determine_elevation_range(pile_data)
            boundary_clipper = BoundaryClipper(site_boundary_coords, elevation_range)
            results.boundary_info = boundary_clipper.get_boundary_info()

            # Quality Assurance Step 3: Validate boundary setup
            logger.info("QA Step 3: Validating boundary setup")
            self._validate_boundary_setup(boundary_clipper, results.boundary_info)

            # Step 4: Clip geometries to boundary
            logger.info("Step 4: Clipping geometries to site boundary")
            clipped_geometries = boundary_clipper.clip_all_geometries(geometries)
            results.clipped_geometries = clipped_geometries

            # Quality Assurance Step 4: Validate clipped geometries
            logger.info("QA Step 4: Validating clipped geometries")
            self._validate_clipped_geometries(clipped_geometries, geometries)
            
            # Step 5: Calculate volumes
            logger.info("Step 5: Calculating volumes")
            volume_results = self.volume_calculator.calculate_all_volumes(clipped_geometries, is_clipped=True)
            
            # Step 6: Analyze overlaps
            logger.info("Step 6: Analyzing overlaps")
            overlap_volumes, detailed_overlaps = self._analyze_overlaps_detailed(clipped_geometries)
            results.overlap_volumes = overlap_volumes
            results.overlap_results = detailed_overlaps

            # Quality Assurance Step 5: Validate overlap analysis
            logger.info("QA Step 5: Validating overlap analysis")
            self._validate_overlap_analysis(detailed_overlaps, overlap_volumes)

            # Step 7: Calculate final volumes (no distribution needed - overlaps are separate)
            logger.info("Step 7: Calculating final volumes")
            results.volume_results = volume_results
            
            # Step 8: Generate reports
            logger.info("Step 8: Generating reports")

            # Generate individual pile report
            individual_report = self.report_generator.generate_individual_pile_report(
                pile_data, volume_results, detailed_overlaps
            )
            results.individual_report = individual_report

            # Generate overlap analysis report
            overlap_report = self.report_generator.generate_overlap_analysis_report(
                detailed_overlaps
            )
            results.overlap_report = overlap_report
            
            summary_report = self.report_generator.generate_summary_report(
                pile_data, volume_results, overlap_volumes, results.boundary_info
            )
            results.summary_report = summary_report
            
            # Step 9: Export reports
            if export_reports:
                logger.info("Step 9: Exporting reports")
                file_paths = self.report_generator.export_reports_to_csv(
                    individual_report, overlap_report, summary_report, output_dir
                )
                logger.info(f"Reports exported to: {file_paths}")
                
            # Step 10: Create visualizations
            if create_visualizations and self.visualizer:
                logger.info("Step 10: Creating visualizations")
                self._create_visualizations(results, output_dir)
                
            logger.info("Pile volume analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Error during pile volume analysis: {e}")
            raise
            
    def _determine_elevation_range(self, pile_data: pd.DataFrame) -> Tuple[float, float]:
        """Determine elevation range from pile data."""
        min_elev = pile_data['founding_level'].min() - 10  # Add buffer
        max_elev = pile_data['pile_cap_bottom_level'].max() + 10
        return min_elev, max_elev
        
    def _create_visualizations(self, results: AnalysisResults, output_dir: Union[str, Path]) -> None:
        """Create and save visualizations and CAD files."""
        if not self.visualizer:
            return

        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        try:
            # Pile geometries visualization
            plotter = self.visualizer.visualize_pile_geometries(
                results.clipped_geometries, results.volume_results, show_parts=False
            )
            plotter.show(screenshot=str(output_path / "pile_geometries.png"))

            # Overlap visualization
            if results.overlap_results:
                plotter = self.visualizer.visualize_overlaps(
                    results.clipped_geometries, results.overlap_results
                )
                plotter.show(screenshot=str(output_path / "pile_overlaps.png"))

            # Site boundary visualization
            if results.pile_data is not None:
                elevation_range = self._determine_elevation_range(results.pile_data)
                boundary_coords = [(coord[0], coord[1]) for coord in results.boundary_info.get('coordinates', [])]

                if boundary_coords:
                    plotter = self.visualizer.visualize_site_boundary(
                        boundary_coords, elevation_range, results.clipped_geometries
                    )
                    plotter.show(screenshot=str(output_path / "site_boundary.png"))

            # Export CAD files
            self._export_cad_files(results, output_path)

            logger.info("Visualizations and CAD files created successfully")

        except Exception as e:
            logger.warning(f"Error creating visualizations: {e}")

    def _export_cad_files(self, results: AnalysisResults, output_dir: Path) -> None:
        """
        Export CAD files for AutoCAD compatibility.

        Args:
            results: Analysis results containing geometries
            output_dir: Output directory for CAD files
        """
        if not results.clipped_geometries:
            logger.warning("No geometries available for CAD export")
            return

        try:
            # Create CAD subdirectory
            cad_dir = output_dir / "cad_files"
            cad_dir.mkdir(exist_ok=True)

            # Export DXF files (AutoCAD format)
            try:
                dxf_files = self.visualizer.export_cad_files(
                    results.clipped_geometries,
                    cad_dir,
                    format='dxf',
                    include_parts=True
                )
                logger.info(f"Exported {len(dxf_files)} DXF files")

            except Exception as e:
                logger.warning(f"Error exporting DXF files: {e}")

            # Export STL files (3D printing/CAD compatible)
            try:
                stl_files = self.visualizer.export_cad_files(
                    results.clipped_geometries,
                    cad_dir,
                    format='stl',
                    include_parts=False  # Combined meshes for STL
                )
                logger.info(f"Exported {len(stl_files)} STL files")

            except Exception as e:
                logger.warning(f"Error exporting STL files: {e}")

        except Exception as e:
            logger.error(f"Error in CAD export process: {e}")
            
    def validate_analysis(self, results: AnalysisResults, tolerance: float = 0.05) -> Dict[str, bool]:
        """
        Validate analysis results.
        
        Args:
            results: Analysis results to validate
            tolerance: Tolerance for validation checks
            
        Returns:
            Dictionary with validation results
        """
        validation = {}
        
        try:
            # Validate volume calculations
            if results.pile_data is not None and results.volume_results:
                theoretical_volumes = self.volume_calculator.calculate_theoretical_volumes(results.pile_data)
                volume_validation = self.volume_calculator.validate_volume_calculations(
                    results.volume_results, theoretical_volumes, tolerance
                )
                validation['volume_calculations'] = all(volume_validation.values())
                validation['volume_details'] = volume_validation
                
            # Validate geometry integrity
            geometry_validation = {}
            for pile_id, geometry in results.geometries.items():
                geometry_validation[pile_id] = self.geometry_engine.validate_mesh(geometry.combined_mesh)
            validation['geometry_integrity'] = all(geometry_validation.values())
            validation['geometry_details'] = geometry_validation
            
            # Validate boundary clipping
            if results.boundary_info:
                validation['boundary_valid'] = results.boundary_info.get('is_valid', False)
                
            logger.info(f"Analysis validation completed: {validation}")
            
        except Exception as e:
            logger.error(f"Error during validation: {e}")
            validation['error'] = str(e)
            
        return validation
        
    def get_analysis_summary(self, results: AnalysisResults) -> str:
        """
        Get a text summary of analysis results.
        
        Args:
            results: Analysis results
            
        Returns:
            Formatted summary string
        """
        if not results.summary_report:
            return "No analysis results available"
            
        summary = results.summary_report
        
        text = f"""
3D Pile Volume Analysis Summary
==============================

Analysis Date: {summary.get('Analysis_Date', 'Unknown')}
Total Piles Analyzed: {summary.get('Total_Piles_Analyzed', 0)}

Site Information:
- Boundary Area: {summary.get('Site_Boundary_Area', 0):.2f} sq units
- Boundary Perimeter: {summary.get('Site_Boundary_Perimeter', 0):.2f} units

Volume Results:
- Total Theoretical Volume: {summary.get('Total_Theoretical_Volume', 0):.3f} cubic units
- Total Final Volume: {summary.get('Total_Final_Volume', 0):.3f} cubic units
- Volume Lost to Overlaps: {summary.get('Total_Volume_Lost_to_Overlaps', 0):.3f} cubic units
- Overall Efficiency: {summary.get('Overall_Volume_Efficiency', 0)*100:.1f}%

Overlap Statistics:
- Total Overlapping Pairs: {summary.get('Total_Overlapping_Pairs', 0)}
- Total Overlap Volume: {summary.get('Total_Overlap_Volume', 0):.3f} cubic units
- Average Overlap Loss: {summary.get('Average_Overlap_Loss_Percentage', 0):.1f}%

Material Breakdown:
- Soil Piles: {summary.get('Soil_Piles_Count', 0)}
- Rock Piles: {summary.get('Rock_Piles_Count', 0)}
"""
        
        return text

    def _analyze_overlaps_detailed(self, geometries: Dict[str, PileGeometry]) -> Tuple[Dict[Tuple[str, str], float], List[OverlapResult]]:
        """
        Analyze overlaps and return both summary volumes and detailed results.

        Args:
            geometries: Dictionary of pile geometries

        Returns:
            Tuple of (overlap_volumes_dict, detailed_overlap_results_list)
        """
        # Find potential overlaps
        potential_pairs = self.overlap_analyzer.find_potential_overlaps(geometries)

        # Analyze each potential overlap
        overlap_volumes = {}
        all_overlaps = []

        for pile1_id, pile2_id in potential_pairs:
            geometry1 = geometries[pile1_id]
            geometry2 = geometries[pile2_id]

            overlaps = self.overlap_analyzer.analyze_overlap(geometry1, geometry2)
            all_overlaps.extend(overlaps)

            # Sum total overlap volume for this pair
            total_overlap = sum(overlap.overlap_volume for overlap in overlaps)

            if total_overlap > 0:
                overlap_volumes[(pile1_id, pile2_id)] = total_overlap

        logger.info(f"Found {len(overlap_volumes)} overlapping pile pairs with "
                   f"{len(all_overlaps)} component overlaps")

        return overlap_volumes, all_overlaps

    def _validate_input_data(self, pile_data: pd.DataFrame, site_boundary_coords: List[Tuple[float, float]]) -> None:
        """
        Quality assurance validation of input data.

        Args:
            pile_data: Processed pile data
            site_boundary_coords: Site boundary coordinates
        """
        logger.info("Validating input data quality...")

        # Validate pile data completeness
        required_columns = ['pile_id', 'x_coord', 'y_coord', 'pile_cap_bottom_level',
                           'target_level', 'founding_level', 'diameter', 'material_type']
        missing_columns = [col for col in required_columns if col not in pile_data.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns in pile data: {missing_columns}")

        # Check for null values in critical columns
        null_counts = pile_data[required_columns].isnull().sum()
        if null_counts.any():
            logger.warning(f"Null values found in pile data: {null_counts[null_counts > 0].to_dict()}")

        # Validate elevation hierarchy
        invalid_elevations = pile_data[
            (pile_data['founding_level'] > pile_data['target_level']) |
            (pile_data['target_level'] > pile_data['pile_cap_bottom_level'])
        ]
        if not invalid_elevations.empty:
            logger.warning(f"Invalid elevation hierarchy found in {len(invalid_elevations)} piles")

        # Validate site boundary
        if len(site_boundary_coords) < 3:
            raise ValueError("Site boundary must have at least 3 points")

        # Check if boundary is closed
        if site_boundary_coords[0] != site_boundary_coords[-1]:
            logger.warning("Site boundary is not closed - will be auto-closed")

        logger.info("✅ Input data validation completed")

    def _validate_geometries(self, geometries: Dict[str, PileGeometry]) -> None:
        """
        Quality assurance validation of created geometries.

        Args:
            geometries: Dictionary of pile geometries
        """
        logger.info("Validating geometry creation quality...")

        invalid_geometries = []
        watertight_issues = []

        for pile_id, geometry in geometries.items():
            # Check if geometries exist
            if geometry.part1_cylinder is None:
                invalid_geometries.append(f"{pile_id}: Part 1 cylinder is None")

            # Check watertight status
            if geometry.part1_cylinder and not geometry.part1_cylinder.is_watertight:
                watertight_issues.append(f"{pile_id}: Part 1 not watertight")

            if geometry.part3_cylinder and not geometry.part3_cylinder.is_watertight:
                watertight_issues.append(f"{pile_id}: Part 3 not watertight")

        if invalid_geometries:
            logger.error(f"Invalid geometries found: {invalid_geometries}")
            raise ValueError("Geometry creation failed for some piles")

        if watertight_issues:
            logger.warning(f"Watertight issues found: {watertight_issues[:5]}...")  # Show first 5

        logger.info(f"✅ Geometry validation completed for {len(geometries)} piles")

    def _validate_boundary_setup(self, boundary_clipper: BoundaryClipper, boundary_info: Dict[str, Any]) -> None:
        """
        Quality assurance validation of boundary setup.

        Args:
            boundary_clipper: Boundary clipper instance
            boundary_info: Boundary information dictionary
        """
        logger.info("Validating boundary setup quality...")

        if not boundary_info.get('is_valid', False):
            logger.warning("Boundary setup validation failed")

        boundary_area = boundary_info.get('area', 0)
        if boundary_area <= 0:
            raise ValueError("Site boundary has zero or negative area")

        logger.info(f"✅ Boundary validation completed - Area: {boundary_area:.2f} sq units")

    def _validate_clipped_geometries(self, clipped_geometries: Dict[str, PileGeometry],
                                   original_geometries: Dict[str, PileGeometry]) -> None:
        """
        Quality assurance validation of clipped geometries.

        Args:
            clipped_geometries: Clipped pile geometries
            original_geometries: Original pile geometries
        """
        logger.info("Validating clipped geometries quality...")

        volume_reductions = []

        for pile_id in original_geometries.keys():
            if pile_id not in clipped_geometries:
                logger.warning(f"Pile {pile_id} missing from clipped geometries")
                continue

            original_vol = self.geometry_engine.compute_mesh_volume(original_geometries[pile_id].combined_mesh)
            clipped_vol = self.geometry_engine.compute_mesh_volume(clipped_geometries[pile_id].combined_mesh)

            if clipped_vol > original_vol:
                logger.warning(f"Pile {pile_id}: Clipped volume ({clipped_vol:.3f}) > original volume ({original_vol:.3f})")

            reduction = (original_vol - clipped_vol) / original_vol if original_vol > 0 else 0
            volume_reductions.append(reduction)

        avg_reduction = sum(volume_reductions) / len(volume_reductions) if volume_reductions else 0
        logger.info(f"✅ Clipped geometry validation completed - Avg volume reduction: {avg_reduction:.1%}")

    def _validate_overlap_analysis(self, overlap_results: List[OverlapResult],
                                 overlap_volumes: Dict[Tuple[str, str], float]) -> None:
        """
        Quality assurance validation of overlap analysis.

        Args:
            overlap_results: Detailed overlap results
            overlap_volumes: Summary overlap volumes
        """
        logger.info("Validating overlap analysis quality...")

        # Check consistency between detailed and summary results
        summary_total = sum(overlap_volumes.values())
        detailed_total = sum(overlap.overlap_volume for overlap in overlap_results)

        if abs(summary_total - detailed_total) > 0.001:
            logger.warning(f"Overlap volume mismatch: Summary={summary_total:.6f}, Detailed={detailed_total:.6f}")

        # Validate overlap types
        valid_types = {'Part2-2', 'Part2-3', 'Part3-3'}
        invalid_types = [overlap.overlap_type for overlap in overlap_results
                        if overlap.overlap_type not in valid_types]

        if invalid_types:
            logger.warning(f"Invalid overlap types found: {set(invalid_types)}")

        # Check minimum volume threshold
        small_overlaps = [overlap for overlap in overlap_results if overlap.overlap_volume < 0.001]
        if small_overlaps:
            logger.warning(f"Found {len(small_overlaps)} overlaps below minimum threshold (0.001 m³)")

        logger.info(f"✅ Overlap analysis validation completed - {len(overlap_results)} overlaps validated")
