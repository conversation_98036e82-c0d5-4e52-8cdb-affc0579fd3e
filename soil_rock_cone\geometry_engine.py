"""
Geometry Engine Module for 3D Pile Volume Analysis

This module handles creation of 3D geometries using Trimesh including:
- Pile cylinders (Part 1)
- Soil/Rock frustums (Part 2) 
- Soil cylinders (Part 3)
- Boolean operations for overlaps and clipping
"""

import trimesh
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class PileGeometry:
    """Container for pile geometry components."""
    pile_id: str
    part1_cylinder: trimesh.Trimesh  # Pile cylinder
    part2_frustum: trimesh.Trimesh   # Soil/Rock frustum
    part3_cylinder: trimesh.Trimesh  # Soil cylinder
    combined_mesh: trimesh.Trimesh   # Union of all parts
    

class GeometryEngine:
    """
    Handles creation and manipulation of 3D pile geometries using Trimesh.
    """
    
    def __init__(self, mesh_resolution: int = 32):
        """
        Initialize the GeometryEngine.
        
        Args:
            mesh_resolution: Number of segments for cylindrical meshes
        """
        self.mesh_resolution = mesh_resolution
        
    def create_pile_geometry(self, pile_data: pd.Series) -> PileGeometry:
        """
        Create complete 3D geometry for a single pile according to specification.

        Geometry structure (from top to bottom):
        - Pile Cap Bottom Level (highest elevation)
        - Target Level (middle elevation)
        - Founding Level (lowest elevation)

        Parts:
        - Part 1: Pile cylinder from Pile Cap Bottom to Founding Level
        - Part 2: Frustum from Founding Level (base) to Target Level (top), tip pointing DOWN
        - Part 3: Soil cylinder from Target Level to Pile Cap Bottom Level

        Args:
            pile_data: Series containing pile parameters

        Returns:
            PileGeometry object with all components
        """
        pile_id = pile_data['pile_id']
        x, y = pile_data['x_coord'], pile_data['y_coord']

        # Extract elevation levels (specification naming)
        pile_cap_bottom_level = pile_data['pile_cap_bottom_level']  # TOP (highest)
        target_level = pile_data['target_level']                    # MIDDLE
        founding_level = pile_data['founding_level']                # BOTTOM (lowest)

        # Extract radii
        pile_radius = pile_data['pile_radius']                      # Pile shaft radius
        frustum_top_radius = pile_data['frustum_top_radius']        # Calculated frustum top radius

        # Part 1: Pile cylinder (full length from top to bottom)
        pile_total_height = pile_cap_bottom_level - founding_level
        part1 = self._create_cylinder(
            center=(x, y, (pile_cap_bottom_level + founding_level) / 2),
            radius=pile_radius,
            height=pile_total_height
        )

        # Part 2: Soil/Rock frustum (from founding level to target level)
        # Handle degenerate case where target_level = founding_level (no frustum)
        frustum_height = target_level - founding_level

        if frustum_height > 0.001:  # Minimum height threshold to avoid degenerate geometry
            # Create frustum with tip pointing DOWN as required
            part2_solid = self._create_frustum(
                center=(x, y, founding_level),
                bottom_radius=pile_radius,        # Small radius at bottom (founding level)
                top_radius=frustum_top_radius,    # Large radius at top (target level)
                height=frustum_height
            )
            # Apply void subtraction per Section 3.5.2: Create "Isolated" geometry
            part2 = self._subtract_mesh(part2_solid, part1)
        else:
            # No frustum - create empty mesh
            logger.debug(f"Pile {pile_id}: No frustum (target_level = founding_level)")
            part2 = self._create_empty_mesh()

        # Part 3: Soil cylinder (from target level to pile cap bottom level)
        soil_cylinder_height = pile_cap_bottom_level - target_level

        if soil_cylinder_height > 0.001:  # Minimum height threshold
            part3_solid = self._create_cylinder(
                center=(x, y, (pile_cap_bottom_level + target_level) / 2),
                radius=frustum_top_radius,        # Same radius as frustum top
                height=soil_cylinder_height
            )
            # Apply void subtraction per Section 3.5.2: Create "Isolated" geometry
            part3 = self._subtract_mesh(part3_solid, part1)
        else:
            # No soil cylinder - create empty mesh
            logger.debug(f"Pile {pile_id}: No soil cylinder (pile_cap_bottom_level = target_level)")
            part3 = self._create_empty_mesh()
        
        # Create combined mesh (union of all parts)
        try:
            combined = self._union_meshes([part1, part2, part3])
        except Exception as e:
            logger.warning(f"Failed to create union for pile {pile_id}: {e}")
            # Fallback: use individual meshes
            combined = part1
            
        return PileGeometry(
            pile_id=pile_id,
            part1_cylinder=part1,
            part2_frustum=part2,
            part3_cylinder=part3,
            combined_mesh=combined
        )
        
    def _create_cylinder(self, center: Tuple[float, float, float],
                        radius: float, height: float) -> trimesh.Trimesh:
        """
        Create a cylindrical mesh.

        Args:
            center: (x, y, z) center coordinates
            radius: Cylinder radius
            height: Cylinder height

        Returns:
            Trimesh cylinder object
        """
        if height <= 0.001 or radius <= 0.001:
            logger.warning(f"Degenerate cylinder: radius={radius}, height={height}")
            return self._create_empty_mesh()

        # Create cylinder primitive (guaranteed watertight)
        cylinder = trimesh.creation.cylinder(
            radius=radius,
            height=height,
            sections=max(8, self.mesh_resolution)  # Minimum 8 sections for stability
        )

        # Translate to correct position
        cylinder.apply_translation(center)

        return cylinder
        
    def _create_frustum(self, center: Tuple[float, float, float],
                       top_radius: float, bottom_radius: float,
                       height: float) -> trimesh.Trimesh:
        """
        Create a frustum (truncated cone) mesh.

        Args:
            center: (x, y, z) center coordinates of bottom
            top_radius: Radius at top of frustum
            bottom_radius: Radius at bottom of frustum
            height: Frustum height

        Returns:
            Trimesh frustum object
        """
        if height <= 0.001 or top_radius <= 0.001 or bottom_radius <= 0.001:
            logger.warning(f"Degenerate frustum: top_r={top_radius}, bottom_r={bottom_radius}, height={height}")
            return self._create_empty_mesh()

        # Use Trimesh's cone primitive and truncate it for better reliability
        try:
            # Create a full cone that extends beyond our frustum
            # Calculate the full cone height needed to create our frustum
            if abs(top_radius - bottom_radius) < 0.001:
                # Radii are essentially equal - create a cylinder instead
                return self._create_cylinder(
                    center=(center[0], center[1], center[2] + height/2),
                    radius=bottom_radius,
                    height=height
                )

            # Calculate full cone parameters
            # Using similar triangles: height_ratio = radius_ratio
            full_height = height * top_radius / (top_radius - bottom_radius)
            cone_bottom_z = center[2] + height - full_height

            # Create full cone
            cone = trimesh.creation.cone(
                radius=top_radius,
                height=full_height,
                sections=max(8, self.mesh_resolution)
            )

            # Position cone so its base is at the frustum top
            cone.apply_translation([center[0], center[1], cone_bottom_z + full_height/2])

            # Create cutting cylinder to truncate the cone at bottom
            if bottom_radius > 0:
                cutting_plane_z = center[2]
                # Create a large cylinder to cut the cone
                cutter = trimesh.creation.cylinder(
                    radius=max(top_radius, bottom_radius) * 2,
                    height=full_height,
                    sections=max(8, self.mesh_resolution)
                )
                cutter.apply_translation([center[0], center[1], cutting_plane_z - full_height/2])

                # Intersect cone with the region above the cutting plane
                # This creates our frustum
                frustum = cone.intersection(cutter)

                if frustum is None or frustum.vertices is None or len(frustum.vertices) == 0:
                    logger.warning("Frustum intersection failed, falling back to manual creation")
                    return self._create_frustum_manual(center, top_radius, bottom_radius, height)

                return frustum
            else:
                return cone

        except Exception as e:
            logger.warning(f"Frustum creation failed: {e}, falling back to manual method")
            return self._create_frustum_manual(center, top_radius, bottom_radius, height)

    def _create_frustum_manual(self, center: Tuple[float, float, float],
                              top_radius: float, bottom_radius: float,
                              height: float) -> trimesh.Trimesh:
        """
        Manual frustum creation as fallback method.

        Args:
            center: (x, y, z) center coordinates of bottom
            top_radius: Radius at top of frustum
            bottom_radius: Radius at bottom of frustum
            height: Frustum height

        Returns:
            Trimesh frustum object
        """
        # Generate vertices for top and bottom circles
        n = max(8, self.mesh_resolution)
        angles = np.linspace(0, 2*np.pi, n, endpoint=False)

        # Bottom circle vertices
        bottom_x = center[0] + bottom_radius * np.cos(angles)
        bottom_y = center[1] + bottom_radius * np.sin(angles)
        bottom_z = np.full_like(bottom_x, center[2])

        # Top circle vertices
        top_x = center[0] + top_radius * np.cos(angles)
        top_y = center[1] + top_radius * np.sin(angles)
        top_z = np.full_like(top_x, center[2] + height)

        # Add center points for top and bottom faces
        vertices = np.vstack([
            np.column_stack([bottom_x, bottom_y, bottom_z]),  # 0 to n-1: bottom circle
            np.column_stack([top_x, top_y, top_z]),           # n to 2n-1: top circle
            [[center[0], center[1], center[2]]],              # 2n: bottom center
            [[center[0], center[1], center[2] + height]]      # 2n+1: top center
        ])

        # Create faces
        faces = []

        # Side faces (quads split into triangles)
        for i in range(n):
            next_i = (i + 1) % n
            # Bottom triangle
            faces.append([i, next_i, n + i])
            # Top triangle
            faces.append([next_i, n + next_i, n + i])

        # Bottom face (fan triangulation from center)
        for i in range(n):
            next_i = (i + 1) % n
            faces.append([2*n, i, next_i])  # bottom center to edge

        # Top face (fan triangulation from center)
        for i in range(n):
            next_i = (i + 1) % n
            faces.append([2*n + 1, n + next_i, n + i])  # top center to edge

        return trimesh.Trimesh(vertices=vertices, faces=faces)

    def _union_meshes(self, meshes: List[trimesh.Trimesh]) -> trimesh.Trimesh:
        """
        Compute union of multiple meshes.
        
        Args:
            meshes: List of Trimesh objects to union
            
        Returns:
            Union mesh
        """
        if not meshes:
            raise ValueError("No meshes provided for union")
            
        if len(meshes) == 1:
            return meshes[0]
            
        # Start with first mesh
        result = meshes[0]
        
        # Sequentially union with remaining meshes
        for mesh in meshes[1:]:
            try:
                result = result.union(mesh)
            except Exception as e:
                logger.warning(f"Union operation failed: {e}")
                # Continue with current result
                
        return result

    def _subtract_mesh(self, mesh_a: trimesh.Trimesh, mesh_b: trimesh.Trimesh) -> trimesh.Trimesh:
        """
        Subtract mesh_b from mesh_a (mesh_a - mesh_b).

        This implements the void subtraction required by Section 3.5.2 to create
        "Isolated" geometries where Part 1 volume is removed from Part 2 and Part 3.

        Args:
            mesh_a: Base mesh (to subtract from)
            mesh_b: Mesh to subtract (void)

        Returns:
            Result mesh with mesh_b volume removed from mesh_a
        """
        try:
            # Perform boolean subtraction
            result = mesh_a.difference(mesh_b)

            # Validate result
            if result is None or result.vertices is None or len(result.vertices) == 0:
                logger.warning("Subtraction resulted in empty mesh, returning original")
                return mesh_a

            # Check if result is watertight
            if not result.is_watertight:
                logger.warning("Subtraction result is not watertight")

            return result

        except Exception as e:
            logger.warning(f"Mesh subtraction failed: {e}, returning original mesh")
            return mesh_a

    def _create_empty_mesh(self) -> trimesh.Trimesh:
        """
        Create an empty mesh for degenerate cases (e.g., zero height frustum/cylinder).

        Returns:
            Empty trimesh with minimal valid geometry
        """
        # Create a minimal valid mesh (single triangle with zero volume)
        vertices = np.array([[0, 0, 0], [0, 0, 0], [0, 0, 0]])
        faces = np.array([[0, 1, 2]])
        return trimesh.Trimesh(vertices=vertices, faces=faces)

    def create_all_pile_geometries(self, pile_data: pd.DataFrame) -> Dict[str, PileGeometry]:
        """
        Create geometries for all piles in the dataset.
        
        Args:
            pile_data: DataFrame with pile information
            
        Returns:
            Dictionary mapping pile_id to PileGeometry
        """
        geometries = {}
        
        for idx, pile in pile_data.iterrows():
            try:
                geometry = self.create_pile_geometry(pile)
                geometries[pile['pile_id']] = geometry
                logger.debug(f"Created geometry for pile {pile['pile_id']}")
            except Exception as e:
                logger.error(f"Failed to create geometry for pile {pile['pile_id']}: {e}")
                
        logger.info(f"Successfully created geometries for {len(geometries)} piles")
        return geometries
        
    def compute_mesh_volume(self, mesh: trimesh.Trimesh) -> float:
        """
        Compute volume of a mesh.
        
        Args:
            mesh: Trimesh object
            
        Returns:
            Volume in cubic units
        """
        try:
            # Ensure mesh is watertight for accurate volume calculation
            if not mesh.is_watertight:
                logger.warning("Mesh is not watertight, volume may be inaccurate")
                
            return float(mesh.volume)
        except Exception as e:
            logger.error(f"Error computing mesh volume: {e}")
            return 0.0
            
    def compute_intersection_volume(self, mesh1: trimesh.Trimesh, 
                                  mesh2: trimesh.Trimesh) -> float:
        """
        Compute volume of intersection between two meshes.
        
        Args:
            mesh1: First mesh
            mesh2: Second mesh
            
        Returns:
            Intersection volume
        """
        try:
            intersection = mesh1.intersection(mesh2)
            return self.compute_mesh_volume(intersection)
        except Exception as e:
            logger.warning(f"Error computing intersection volume: {e}")
            return 0.0
            
    def validate_mesh(self, mesh: trimesh.Trimesh) -> bool:
        """
        Validate mesh integrity.
        
        Args:
            mesh: Trimesh object to validate
            
        Returns:
            True if mesh is valid
        """
        try:
            # Check basic properties
            if mesh.vertices is None or len(mesh.vertices) == 0:
                return False
            if mesh.faces is None or len(mesh.faces) == 0:
                return False
                
            # Check for degenerate faces
            if mesh.area == 0:
                return False
                
            return True
        except Exception:
            return False
