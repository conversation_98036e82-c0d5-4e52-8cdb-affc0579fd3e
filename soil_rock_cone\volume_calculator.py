"""
Volume Calculator Module for 3D Pile Volume Analysis

This module handles volume calculations for individual pile components
and manages volume distribution for overlapping geometries. Supports
both Trimesh and CadQuery objects for volume computation.
"""

import trimesh
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass

try:
    import cadquery as cq
    CADQUERY_AVAILABLE = True
except ImportError:
    CADQUERY_AVAILABLE = False
    logging.warning("CadQuery not available - CadQuery solid volume calculations disabled")

from .geometry_engine import PileGeometry

logger = logging.getLogger(__name__)


@dataclass
class VolumeResult:
    """Container for volume calculation results."""
    pile_id: str
    part1_volume: float  # Pile cylinder volume
    part2_volume: float  # Frustum volume
    part3_volume: float  # Soil cylinder volume
    total_volume: float  # Total volume
    clipped_volume: float  # Volume after boundary clipping
    overlap_volume: float  # Volume lost to overlaps
    final_volume: float  # Final assigned volume after overlap distribution


class VolumeCalculator:
    """
    Handles volume calculations and distribution for pile geometries.
    """
    
    def __init__(self):
        """Initialize the VolumeCalculator."""
        pass
        
    def calculate_pile_volume(self, geometry: PileGeometry, 
                            is_clipped: bool = False) -> VolumeResult:
        """
        Calculate volumes for all components of a pile geometry.
        
        Args:
            geometry: Pile geometry object
            is_clipped: Whether geometry has been clipped to boundary
            
        Returns:
            VolumeResult with all volume calculations
        """
        try:
            # Calculate individual component volumes
            part1_vol = self._calculate_mesh_volume(geometry.part1_cylinder)
            part2_vol = self._calculate_mesh_volume(geometry.part2_frustum)
            part3_vol = self._calculate_mesh_volume(geometry.part3_cylinder)
            
            # Calculate total volume
            total_vol = part1_vol + part2_vol + part3_vol
            
            # Calculate combined mesh volume (may differ due to overlaps within pile)
            combined_vol = self._calculate_mesh_volume(geometry.combined_mesh)
            
            # Use combined volume as clipped volume if geometry is clipped
            clipped_vol = combined_vol if is_clipped else total_vol
            
            return VolumeResult(
                pile_id=geometry.pile_id,
                part1_volume=part1_vol,
                part2_volume=part2_vol,
                part3_volume=part3_vol,
                total_volume=total_vol,
                clipped_volume=clipped_vol,
                overlap_volume=0.0,  # Will be calculated later
                final_volume=clipped_vol  # Initial assignment
            )
            
        except Exception as e:
            logger.error(f"Error calculating volume for pile {geometry.pile_id}: {e}")
            return VolumeResult(
                pile_id=geometry.pile_id,
                part1_volume=0.0,
                part2_volume=0.0,
                part3_volume=0.0,
                total_volume=0.0,
                clipped_volume=0.0,
                overlap_volume=0.0,
                final_volume=0.0
            )
            
    def _calculate_mesh_volume(self, mesh: trimesh.Trimesh) -> float:
        """
        Calculate volume of a mesh with error handling.
        
        Args:
            mesh: Trimesh object
            
        Returns:
            Volume in cubic units
        """
        try:
            if mesh is None or mesh.vertices is None or len(mesh.vertices) == 0:
                return 0.0
                
            # Check if mesh is watertight
            if not mesh.is_watertight:
                logger.warning("Mesh is not watertight, attempting repair")
                mesh = mesh.fill_holes()
                
            volume = float(mesh.volume)
            
            # Ensure positive volume
            if volume < 0:
                logger.warning("Negative volume detected, taking absolute value")
                volume = abs(volume)
                
            return volume
            
        except Exception as e:
            logger.warning(f"Error calculating mesh volume: {e}")
            return 0.0

    def _calculate_cadquery_solid_volume(self, solid: 'cq.Workplane') -> float:
        """
        Calculate volume of a CadQuery solid with error handling.

        Args:
            solid: CadQuery Workplane object

        Returns:
            Volume in cubic units
        """
        if not CADQUERY_AVAILABLE:
            logger.warning("CadQuery not available - cannot calculate solid volume")
            return 0.0

        try:
            if solid is None:
                return 0.0

            # Get the solid object from the workplane
            solid_obj = solid.val()

            # Calculate volume using CadQuery's built-in volume calculation
            volume = float(solid_obj.Volume())

            # Ensure positive volume
            if volume < 0:
                logger.warning("Negative volume detected in CadQuery solid, taking absolute value")
                volume = abs(volume)

            return volume

        except Exception as e:
            logger.warning(f"Error calculating CadQuery solid volume: {e}")
            return 0.0

    def calculate_volume_from_object(self, obj: Union[trimesh.Trimesh, 'cq.Workplane']) -> float:
        """
        Calculate volume from either a Trimesh or CadQuery object.

        Args:
            obj: Either a Trimesh or CadQuery Workplane object

        Returns:
            Volume in cubic units
        """
        try:
            if isinstance(obj, trimesh.Trimesh):
                return self._calculate_mesh_volume(obj)
            elif CADQUERY_AVAILABLE and isinstance(obj, cq.Workplane):
                return self._calculate_cadquery_solid_volume(obj)
            else:
                logger.warning(f"Unsupported object type for volume calculation: {type(obj)}")
                return 0.0

        except Exception as e:
            logger.warning(f"Error calculating volume from object: {e}")
            return 0.0

    def calculate_all_volumes(self, geometries: Dict[str, PileGeometry],
                            is_clipped: bool = False) -> Dict[str, VolumeResult]:
        """
        Calculate volumes for all pile geometries.
        
        Args:
            geometries: Dictionary of pile geometries
            is_clipped: Whether geometries have been clipped
            
        Returns:
            Dictionary mapping pile_id to VolumeResult
        """
        volume_results = {}
        
        for pile_id, geometry in geometries.items():
            volume_results[pile_id] = self.calculate_pile_volume(geometry, is_clipped)
            
        logger.info(f"Calculated volumes for {len(volume_results)} piles")
        return volume_results
        
    def distribute_overlap_volumes(self, volume_results: Dict[str, VolumeResult],
                                 overlap_data: Dict[Tuple[str, str], float]) -> Dict[str, VolumeResult]:
        """
        Distribute overlapping volumes among contributing piles.
        
        Args:
            volume_results: Current volume results
            overlap_data: Dictionary mapping pile pairs to overlap volumes
            
        Returns:
            Updated volume results with overlap distribution
        """
        # Create copy of results to modify
        updated_results = {k: VolumeResult(**v.__dict__) for k, v in volume_results.items()}
        
        # Track total overlap volume per pile
        pile_overlaps = {pile_id: 0.0 for pile_id in volume_results.keys()}
        
        # Process each overlap
        for (pile1_id, pile2_id), overlap_volume in overlap_data.items():
            if overlap_volume <= 0:
                continue
                
            # Get volumes of contributing piles
            vol1 = volume_results[pile1_id].clipped_volume
            vol2 = volume_results[pile2_id].clipped_volume
            
            if vol1 + vol2 == 0:
                continue
                
            # Distribute overlap proportionally
            ratio1 = vol1 / (vol1 + vol2)
            ratio2 = vol2 / (vol1 + vol2)
            
            overlap1 = overlap_volume * ratio1
            overlap2 = overlap_volume * ratio2
            
            # Update overlap tracking
            pile_overlaps[pile1_id] += overlap1
            pile_overlaps[pile2_id] += overlap2
            
        # Update final volumes
        for pile_id, total_overlap in pile_overlaps.items():
            result = updated_results[pile_id]
            result.overlap_volume = total_overlap
            result.final_volume = max(0.0, result.clipped_volume - total_overlap)
            
        logger.info(f"Distributed overlap volumes for {len(overlap_data)} overlaps")
        return updated_results
        
    def calculate_theoretical_volumes(self, pile_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate theoretical volumes using analytical formulas.
        
        Args:
            pile_data: DataFrame with pile parameters
            
        Returns:
            DataFrame with theoretical volume calculations
        """
        result = pile_data.copy()
        
        # Part 1: Pile cylinder volume
        result['part1_theoretical'] = (
            np.pi * result['pile_radius']**2 * result['pile_total_length']
        )

        # Part 2: Frustum volume (truncated cone)
        # V = (π * h / 3) * (r1² + r1*r2 + r2²)
        r1 = result['pile_radius']  # Bottom radius (at founding level)
        r2 = result['frustum_top_radius']  # Top radius (at target level)
        h = result['frustum_height']

        result['part2_theoretical'] = (
            np.pi * h / 3 * (r1**2 + r1*r2 + r2**2)
        )
        
        # Part 3: Soil cylinder volume (from target level to pile cap bottom level)
        part3_height = result['soil_cylinder_height']
        result['part3_theoretical'] = np.pi * r2**2 * part3_height
        
        # Total theoretical volume
        result['total_theoretical'] = (
            result['part1_theoretical'] + 
            result['part2_theoretical'] + 
            result['part3_theoretical']
        )
        
        return result
        
    def validate_volume_calculations(self, volume_results: Dict[str, VolumeResult],
                                   theoretical_volumes: pd.DataFrame,
                                   tolerance: float = 0.05) -> Dict[str, bool]:
        """
        Validate calculated volumes against theoretical values.
        
        Args:
            volume_results: Calculated volume results
            theoretical_volumes: DataFrame with theoretical calculations
            tolerance: Relative tolerance for validation
            
        Returns:
            Dictionary mapping pile_id to validation status
        """
        validation_results = {}
        
        for pile_id, result in volume_results.items():
            try:
                # Find corresponding theoretical values
                theoretical_row = theoretical_volumes[
                    theoretical_volumes['pile_id'] == pile_id
                ].iloc[0]
                
                # Compare total volumes
                calculated = result.total_volume
                theoretical = theoretical_row['total_theoretical']
                
                if theoretical == 0:
                    validation_results[pile_id] = calculated == 0
                else:
                    relative_error = abs(calculated - theoretical) / theoretical
                    validation_results[pile_id] = relative_error <= tolerance
                    
                    if relative_error > tolerance:
                        logger.warning(
                            f"Volume validation failed for pile {pile_id}: "
                            f"calculated={calculated:.3f}, theoretical={theoretical:.3f}, "
                            f"error={relative_error:.3%}"
                        )
                        
            except Exception as e:
                logger.error(f"Error validating volume for pile {pile_id}: {e}")
                validation_results[pile_id] = False
                
        passed = sum(validation_results.values())
        total = len(validation_results)
        logger.info(f"Volume validation: {passed}/{total} piles passed")
        
        return validation_results
        
    def get_volume_summary(self, volume_results: Dict[str, VolumeResult]) -> Dict[str, Union[float, int, bool]]:
        """
        Generate summary statistics for volume calculations.

        Args:
            volume_results: Volume calculation results

        Returns:
            Dictionary with summary statistics
        """
        if not volume_results:
            return {'cadquery_available': CADQUERY_AVAILABLE}

        # Extract volumes
        total_volumes = [r.total_volume for r in volume_results.values()]
        final_volumes = [r.final_volume for r in volume_results.values()]
        overlap_volumes = [r.overlap_volume for r in volume_results.values()]

        return {
            'total_piles': len(volume_results),
            'total_volume_sum': sum(total_volumes),
            'final_volume_sum': sum(final_volumes),
            'total_overlap_volume': sum(overlap_volumes),
            'average_pile_volume': np.mean(total_volumes),
            'max_pile_volume': max(total_volumes),
            'min_pile_volume': min(total_volumes),
            'volume_efficiency': sum(final_volumes) / sum(total_volumes) if sum(total_volumes) > 0 else 0,
            'cadquery_available': CADQUERY_AVAILABLE
        }

    def validate_trimesh_vs_cadquery_volumes(self,
                                           trimesh_objects: List[trimesh.Trimesh],
                                           cadquery_objects: List['cq.Workplane'],
                                           tolerance: float = 0.01) -> Dict[str, Union[bool, float, List[float]]]:
        """
        Validate volume calculations between Trimesh and CadQuery objects.

        Args:
            trimesh_objects: List of Trimesh objects
            cadquery_objects: List of corresponding CadQuery objects
            tolerance: Relative tolerance for validation

        Returns:
            Dictionary with validation results
        """
        if not CADQUERY_AVAILABLE:
            return {
                'validation_possible': False,
                'reason': 'CadQuery not available'
            }

        if len(trimesh_objects) != len(cadquery_objects):
            return {
                'validation_possible': False,
                'reason': 'Mismatched object counts'
            }

        try:
            trimesh_volumes = [self._calculate_mesh_volume(mesh) for mesh in trimesh_objects]
            cadquery_volumes = [self._calculate_cadquery_solid_volume(solid) for solid in cadquery_objects]

            relative_errors = []
            all_within_tolerance = True

            for i, (trim_vol, cad_vol) in enumerate(zip(trimesh_volumes, cadquery_volumes)):
                if trim_vol == 0 and cad_vol == 0:
                    relative_errors.append(0.0)
                elif trim_vol == 0 or cad_vol == 0:
                    relative_errors.append(float('inf'))
                    all_within_tolerance = False
                else:
                    rel_error = abs(trim_vol - cad_vol) / max(trim_vol, cad_vol)
                    relative_errors.append(rel_error)
                    if rel_error > tolerance:
                        all_within_tolerance = False
                        logger.warning(
                            f"Volume validation failed for object {i}: "
                            f"Trimesh={trim_vol:.3f}, CadQuery={cad_vol:.3f}, "
                            f"error={rel_error:.3%}"
                        )

            return {
                'validation_possible': True,
                'all_within_tolerance': all_within_tolerance,
                'tolerance_used': tolerance,
                'trimesh_volumes': trimesh_volumes,
                'cadquery_volumes': cadquery_volumes,
                'relative_errors': relative_errors,
                'max_relative_error': max(relative_errors) if relative_errors else 0.0,
                'mean_relative_error': np.mean(relative_errors) if relative_errors else 0.0
            }

        except Exception as e:
            logger.error(f"Error validating Trimesh vs CadQuery volumes: {e}")
            return {
                'validation_possible': False,
                'reason': f'Validation error: {e}'
            }
