"""
Visualizer Module for 3D Pile Volume Analysis

This module handles 3D visualization of pile geometries, overlaps, and
site boundaries using PyVista for interactive and static rendering.
It also provides CAD file export functionality for AutoCAD compatibility.
"""

import pyvista as pv
import trimesh
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from pathlib import Path

try:
    import ezdxf
    from ezdxf.render import forms
    EZDXF_AVAILABLE = True
except ImportError:
    EZDXF_AVAILABLE = False
    logging.warning("ezdxf not available - CAD export functionality disabled")

from .geometry_engine import PileGeometry
from .volume_calculator import VolumeResult
from .overlap_analyzer import OverlapResult

logger = logging.getLogger(__name__)


class Visualizer:
    """
    Handles 3D visualization of pile volume analysis results using PyVista.
    """
    
    def __init__(self, window_size: Tuple[int, int] = (1200, 800)):
        """
        Initialize the Visualizer.
        
        Args:
            window_size: Window size for PyVista plots
        """
        self.window_size = window_size
        
        # Set PyVista theme
        pv.set_plot_theme('document')
        pv.global_theme.window_size = window_size
        pv.global_theme.show_scalar_bar = True
        pv.global_theme.font.size = 12
        
    def visualize_pile_geometries(self, 
                                geometries: Dict[str, PileGeometry],
                                volume_results: Optional[Dict[str, VolumeResult]] = None,
                                show_parts: bool = True,
                                show_labels: bool = True) -> pv.Plotter:
        """
        Visualize pile geometries with optional volume coloring.
        
        Args:
            geometries: Dictionary of pile geometries
            volume_results: Optional volume results for coloring
            show_parts: Whether to show individual parts
            show_labels: Whether to show pile labels
            
        Returns:
            PyVista plotter object
        """
        plotter = pv.Plotter(window_size=self.window_size)
        
        if not geometries:
            logger.warning("No geometries to visualize")
            return plotter
            
        # Color scheme
        colors = {
            'part1': 'lightblue',    # Pile cylinder
            'part2': 'orange',       # Frustum
            'part3': 'lightgreen',   # Soil cylinder
            'combined': 'gray'
        }
        
        # Add each pile geometry
        for pile_id, geometry in geometries.items():
            try:
                if show_parts:
                    # Add individual parts
                    self._add_trimesh_to_plotter(
                        plotter, geometry.part1_cylinder, 
                        color=colors['part1'], 
                        label=f"{pile_id}_Part1" if len(geometries) <= 10 else None
                    )
                    self._add_trimesh_to_plotter(
                        plotter, geometry.part2_frustum, 
                        color=colors['part2'],
                        label=f"{pile_id}_Part2" if len(geometries) <= 10 else None
                    )
                    self._add_trimesh_to_plotter(
                        plotter, geometry.part3_cylinder, 
                        color=colors['part3'],
                        label=f"{pile_id}_Part3" if len(geometries) <= 10 else None
                    )
                else:
                    # Add combined geometry
                    color = colors['combined']
                    if volume_results and pile_id in volume_results:
                        # Color by volume
                        volume = volume_results[pile_id].final_volume
                        color = volume  # Will be mapped to colormap
                        
                    self._add_trimesh_to_plotter(
                        plotter, geometry.combined_mesh,
                        color=color,
                        label=pile_id if len(geometries) <= 20 else None
                    )
                    
                # Add pile labels
                if show_labels and len(geometries) <= 50:
                    center = geometry.combined_mesh.centroid
                    plotter.add_point_labels(
                        [center], [pile_id],
                        point_size=0, font_size=10,
                        text_color='black'
                    )
                    
            except Exception as e:
                logger.warning(f"Error visualizing pile {pile_id}: {e}")
                
        # Configure view
        plotter.add_axes()
        plotter.show_grid()
        
        if volume_results and not show_parts:
            plotter.add_scalar_bar(title="Final Volume")
            
        plotter.camera_position = 'iso'
        
        return plotter
        
    def visualize_overlaps(self, 
                          geometries: Dict[str, PileGeometry],
                          overlap_results: List[OverlapResult],
                          highlight_overlaps: bool = True) -> pv.Plotter:
        """
        Visualize pile geometries with overlap highlighting.
        
        Args:
            geometries: Dictionary of pile geometries
            overlap_results: List of overlap results
            highlight_overlaps: Whether to highlight overlapping regions
            
        Returns:
            PyVista plotter object
        """
        plotter = pv.Plotter(window_size=self.window_size)
        
        # Get overlapping pile IDs
        overlapping_piles = set()
        for overlap in overlap_results:
            overlapping_piles.add(overlap.pile1_id)
            overlapping_piles.add(overlap.pile2_id)
            
        # Add pile geometries
        for pile_id, geometry in geometries.items():
            try:
                color = 'red' if pile_id in overlapping_piles else 'lightblue'
                opacity = 0.7 if pile_id in overlapping_piles else 0.9
                
                self._add_trimesh_to_plotter(
                    plotter, geometry.combined_mesh,
                    color=color, opacity=opacity,
                    label=pile_id if len(geometries) <= 20 else None
                )
                
            except Exception as e:
                logger.warning(f"Error visualizing pile {pile_id}: {e}")
                
        # Add overlap regions
        if highlight_overlaps:
            for overlap in overlap_results:
                if overlap.overlap_mesh is not None:
                    try:
                        self._add_trimesh_to_plotter(
                            plotter, overlap.overlap_mesh,
                            color='yellow', opacity=0.8,
                            label=f"Overlap_{overlap.pile1_id}_{overlap.pile2_id}"
                        )
                    except Exception as e:
                        logger.warning(f"Error visualizing overlap: {e}")
                        
        plotter.add_axes()
        plotter.show_grid()
        plotter.camera_position = 'iso'
        
        return plotter
        
    def visualize_site_boundary(self, 
                              boundary_coords: List[Tuple[float, float]],
                              elevation_range: Tuple[float, float],
                              geometries: Optional[Dict[str, PileGeometry]] = None) -> pv.Plotter:
        """
        Visualize site boundary with optional pile geometries.
        
        Args:
            boundary_coords: Site boundary coordinates
            elevation_range: (min_elevation, max_elevation)
            geometries: Optional pile geometries to include
            
        Returns:
            PyVista plotter object
        """
        plotter = pv.Plotter(window_size=self.window_size)
        
        # Create boundary mesh
        try:
            boundary_mesh = self._create_boundary_mesh(boundary_coords, elevation_range)
            plotter.add_mesh(
                boundary_mesh, 
                color='lightgray', 
                opacity=0.3,
                show_edges=True,
                edge_color='black',
                label='Site Boundary'
            )
        except Exception as e:
            logger.error(f"Error creating boundary visualization: {e}")
            
        # Add pile geometries if provided
        if geometries:
            for pile_id, geometry in geometries.items():
                try:
                    self._add_trimesh_to_plotter(
                        plotter, geometry.combined_mesh,
                        color='blue', opacity=0.8,
                        label=pile_id if len(geometries) <= 20 else None
                    )
                except Exception as e:
                    logger.warning(f"Error adding pile {pile_id}: {e}")
                    
        plotter.add_axes()
        plotter.show_grid()
        plotter.camera_position = 'iso'
        
        return plotter
        
    def _add_trimesh_to_plotter(self, plotter: pv.Plotter, mesh: trimesh.Trimesh,
                               color: Union[str, float] = 'blue', 
                               opacity: float = 1.0,
                               label: Optional[str] = None) -> None:
        """
        Add a Trimesh object to PyVista plotter.
        
        Args:
            plotter: PyVista plotter
            mesh: Trimesh object
            color: Color specification
            opacity: Mesh opacity
            label: Optional label for legend
        """
        try:
            # Convert Trimesh to PyVista
            if mesh.vertices is None or len(mesh.vertices) == 0:
                logger.warning("Empty mesh, skipping")
                return
                
            # Create PyVista mesh
            faces = mesh.faces
            if len(faces) > 0:
                # Convert faces to PyVista format (add count prefix)
                pv_faces = np.column_stack([
                    np.full(len(faces), 3),  # Triangle count
                    faces
                ]).flatten()
                
                pv_mesh = pv.PolyData(mesh.vertices, pv_faces)
            else:
                # Point cloud
                pv_mesh = pv.PolyData(mesh.vertices)
                
            # Add to plotter
            plotter.add_mesh(
                pv_mesh,
                color=color,
                opacity=opacity,
                label=label,
                show_edges=False
            )
            
        except Exception as e:
            logger.warning(f"Error adding mesh to plotter: {e}")
            
    def _create_boundary_mesh(self, 
                            boundary_coords: List[Tuple[float, float]],
                            elevation_range: Tuple[float, float]) -> pv.PolyData:
        """
        Create PyVista mesh for site boundary.
        
        Args:
            boundary_coords: Boundary coordinates
            elevation_range: Elevation range
            
        Returns:
            PyVista boundary mesh
        """
        min_elev, max_elev = elevation_range
        height = max_elev - min_elev
        
        # Create bottom and top polygons
        coords = np.array(boundary_coords)
        n_points = len(coords)
        
        # Bottom vertices
        bottom_vertices = np.column_stack([
            coords[:, 0], coords[:, 1], 
            np.full(n_points, min_elev)
        ])
        
        # Top vertices
        top_vertices = np.column_stack([
            coords[:, 0], coords[:, 1], 
            np.full(n_points, max_elev)
        ])
        
        # Combine vertices
        vertices = np.vstack([bottom_vertices, top_vertices])
        
        # Create faces
        faces = []
        
        # Bottom face
        bottom_face = [n_points] + list(range(n_points))
        faces.extend(bottom_face)
        
        # Top face (reverse order)
        top_face = [n_points] + list(range(n_points, 2*n_points))[::-1]
        faces.extend(top_face)
        
        # Side faces
        for i in range(n_points):
            next_i = (i + 1) % n_points
            # Two triangles per side
            faces.extend([3, i, next_i, n_points + i])
            faces.extend([3, next_i, n_points + next_i, n_points + i])
            
        return pv.PolyData(vertices, faces)
        
    def save_visualization(self, plotter: pv.Plotter, 
                         filename: Union[str, Path],
                         format: str = 'png') -> str:
        """
        Save visualization to file.
        
        Args:
            plotter: PyVista plotter
            filename: Output filename
            format: Output format ('png', 'jpg', 'svg')
            
        Returns:
            Path to saved file
        """
        try:
            filepath = Path(filename).with_suffix(f'.{format}')
            plotter.screenshot(str(filepath))
            logger.info(f"Visualization saved to {filepath}")
            return str(filepath)
        except Exception as e:
            logger.error(f"Error saving visualization: {e}")
            raise
            
    def create_volume_comparison_plot(self, 
                                    volume_results: Dict[str, VolumeResult]) -> pv.Plotter:
        """
        Create a plot comparing theoretical vs calculated volumes.
        
        Args:
            volume_results: Volume calculation results
            
        Returns:
            PyVista plotter with comparison visualization
        """
        plotter = pv.Plotter(window_size=self.window_size)
        
        # Extract data for plotting
        pile_ids = list(volume_results.keys())
        calculated_volumes = [r.total_volume for r in volume_results.values()]
        final_volumes = [r.final_volume for r in volume_results.values()]
        
        # Create bar chart-like visualization
        # This is a simplified approach - for detailed charts, consider matplotlib
        
        plotter.add_text("Volume Comparison", position='upper_left', font_size=16)
        
        return plotter

    def export_cad_files(self,
                        geometries: Dict[str, PileGeometry],
                        output_dir: Union[str, Path],
                        format: str = 'dxf',
                        include_parts: bool = True) -> List[str]:
        """
        Export pile geometries to CAD files compatible with AutoCAD.

        Args:
            geometries: Dictionary of pile geometries to export
            output_dir: Output directory for CAD files
            format: Export format ('dxf', 'stl')
            include_parts: Whether to export individual parts or combined mesh only

        Returns:
            List of exported file paths

        Raises:
            ValueError: If unsupported format or ezdxf not available
            IOError: If file writing fails
        """
        if not EZDXF_AVAILABLE and format.lower() == 'dxf':
            raise ValueError("ezdxf library not available - cannot export DXF files")

        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        exported_files = []

        try:
            if format.lower() == 'dxf':
                exported_files.extend(self._export_dxf_files(geometries, output_path, include_parts))
            elif format.lower() == 'stl':
                exported_files.extend(self._export_stl_files(geometries, output_path, include_parts))
            else:
                raise ValueError(f"Unsupported export format: {format}")

            logger.info(f"Successfully exported {len(exported_files)} CAD files to {output_path}")
            return exported_files

        except Exception as e:
            logger.error(f"Error exporting CAD files: {e}")
            raise

    def _export_dxf_files(self,
                         geometries: Dict[str, PileGeometry],
                         output_path: Path,
                         include_parts: bool) -> List[str]:
        """
        Export geometries to DXF format using ezdxf.

        Args:
            geometries: Pile geometries to export
            output_path: Output directory path
            include_parts: Whether to include individual parts

        Returns:
            List of exported DXF file paths
        """
        exported_files = []

        # Create combined DXF file with all piles
        doc = ezdxf.new('R2010')  # AutoCAD 2010 format for compatibility
        msp = doc.modelspace()

        # Define colors for different parts
        part_colors = {
            'part1': 1,  # Red - Pile cylinder
            'part2': 3,  # Green - Frustum
            'part3': 5,  # Blue - Soil cylinder
            'combined': 7  # White/Gray - Combined mesh
        }

        for pile_id, geometry in geometries.items():
            try:
                if include_parts:
                    # Export individual parts
                    self._add_trimesh_to_dxf(msp, geometry.part1_cylinder,
                                           f"{pile_id}_Part1", part_colors['part1'])
                    self._add_trimesh_to_dxf(msp, geometry.part2_frustum,
                                           f"{pile_id}_Part2", part_colors['part2'])
                    self._add_trimesh_to_dxf(msp, geometry.part3_cylinder,
                                           f"{pile_id}_Part3", part_colors['part3'])
                else:
                    # Export combined mesh
                    self._add_trimesh_to_dxf(msp, geometry.combined_mesh,
                                           pile_id, part_colors['combined'])

            except Exception as e:
                logger.warning(f"Error adding pile {pile_id} to DXF: {e}")

        # Save combined file
        combined_file = output_path / "pile_geometries_combined.dxf"
        doc.saveas(str(combined_file))
        exported_files.append(str(combined_file))

        # Export individual pile files if requested
        if len(geometries) <= 20:  # Avoid too many files
            for pile_id, geometry in geometries.items():
                try:
                    pile_doc = ezdxf.new('R2010')
                    pile_msp = pile_doc.modelspace()

                    if include_parts:
                        self._add_trimesh_to_dxf(pile_msp, geometry.part1_cylinder,
                                               "Part1", part_colors['part1'])
                        self._add_trimesh_to_dxf(pile_msp, geometry.part2_frustum,
                                               "Part2", part_colors['part2'])
                        self._add_trimesh_to_dxf(pile_msp, geometry.part3_cylinder,
                                               "Part3", part_colors['part3'])
                    else:
                        self._add_trimesh_to_dxf(pile_msp, geometry.combined_mesh,
                                               "Pile", part_colors['combined'])

                    pile_file = output_path / f"{pile_id}.dxf"
                    pile_doc.saveas(str(pile_file))
                    exported_files.append(str(pile_file))

                except Exception as e:
                    logger.warning(f"Error exporting individual pile {pile_id}: {e}")

        return exported_files

    def _add_trimesh_to_dxf(self,
                           msp,
                           mesh: trimesh.Trimesh,
                           layer_name: str,
                           color: int) -> None:
        """
        Add a Trimesh object to DXF modelspace as a 3D solid-like representation.

        Creates a more solid-like representation using POLYFACE MESH instead of individual 3D faces.
        This provides better 3D block representation for CAD software.

        Args:
            msp: DXF modelspace
            mesh: Trimesh object to add
            layer_name: Layer name for the mesh
            color: AutoCAD color index
        """
        try:
            if mesh.vertices is None or len(mesh.vertices) == 0:
                logger.warning(f"Empty mesh for layer {layer_name}, skipping")
                return

            # Create layer if it doesn't exist
            if layer_name not in msp.doc.layers:
                msp.doc.layers.new(layer_name, dxfattribs={'color': color})

            # Create a POLYFACE MESH for better 3D solid representation
            vertices = mesh.vertices
            faces = mesh.faces

            # Convert to format suitable for polyface mesh
            mesh_vertices = [tuple(v) for v in vertices]
            mesh_faces = []

            for face in faces:
                # Convert face indices to 1-based (DXF requirement) and add face
                face_indices = [i + 1 for i in face]
                mesh_faces.append(face_indices)

            # Create POLYFACE MESH entity
            polyface = msp.add_polyface()

            # Add vertices
            for vertex in mesh_vertices:
                polyface.append_vertex(vertex, dxfattribs={'layer': layer_name})

            # Add faces
            for face_indices in mesh_faces:
                polyface.append_face(face_indices, dxfattribs={'layer': layer_name, 'color': color})

            # Set layer and color for the entire polyface
            polyface.dxf.layer = layer_name
            polyface.dxf.color = color

            logger.debug(f"Added polyface mesh to layer {layer_name} with {len(mesh_vertices)} vertices and {len(mesh_faces)} faces")

        except Exception as e:
            logger.warning(f"Error adding mesh to DXF layer {layer_name}: {e}")
            # Fallback to 3D faces if polyface fails
            self._add_trimesh_to_dxf_fallback(msp, mesh, layer_name, color)

    def _add_trimesh_to_dxf_fallback(self,
                                   msp,
                                   mesh: trimesh.Trimesh,
                                   layer_name: str,
                                   color: int) -> None:
        """
        Fallback method using individual 3D faces.
        """
        try:
            vertices = mesh.vertices
            faces = mesh.faces

            for face in faces:
                # Get face vertices
                v1, v2, v3 = vertices[face]

                # Create 3DFACE entity
                msp.add_3dface(
                    [tuple(v1), tuple(v2), tuple(v3), tuple(v3)],  # 4th point same as 3rd for triangle
                    dxfattribs={
                        'layer': layer_name,
                        'color': color
                    }
                )

        except Exception as e:
            logger.warning(f"Error in fallback DXF export for layer {layer_name}: {e}")

    def _export_stl_files(self,
                         geometries: Dict[str, PileGeometry],
                         output_path: Path,
                         include_parts: bool) -> List[str]:
        """
        Export geometries to STL format.

        Args:
            geometries: Pile geometries to export
            output_path: Output directory path
            include_parts: Whether to include individual parts

        Returns:
            List of exported STL file paths
        """
        exported_files = []

        # Export combined STL file
        try:
            combined_meshes = []
            for geometry in geometries.values():
                if include_parts:
                    combined_meshes.extend([
                        geometry.part1_cylinder,
                        geometry.part2_frustum,
                        geometry.part3_cylinder
                    ])
                else:
                    combined_meshes.append(geometry.combined_mesh)

            if combined_meshes:
                # Combine all meshes
                combined_mesh = trimesh.util.concatenate(combined_meshes)
                combined_file = output_path / "pile_geometries_combined.stl"
                combined_mesh.export(str(combined_file))
                exported_files.append(str(combined_file))

        except Exception as e:
            logger.warning(f"Error creating combined STL file: {e}")

        # Export individual pile STL files if requested
        if len(geometries) <= 20:  # Avoid too many files
            for pile_id, geometry in geometries.items():
                try:
                    if include_parts:
                        # Export parts separately
                        part1_file = output_path / f"{pile_id}_Part1.stl"
                        geometry.part1_cylinder.export(str(part1_file))
                        exported_files.append(str(part1_file))

                        part2_file = output_path / f"{pile_id}_Part2.stl"
                        geometry.part2_frustum.export(str(part2_file))
                        exported_files.append(str(part2_file))

                        part3_file = output_path / f"{pile_id}_Part3.stl"
                        geometry.part3_cylinder.export(str(part3_file))
                        exported_files.append(str(part3_file))
                    else:
                        # Export combined mesh
                        pile_file = output_path / f"{pile_id}.stl"
                        geometry.combined_mesh.export(str(pile_file))
                        exported_files.append(str(pile_file))

                except Exception as e:
                    logger.warning(f"Error exporting STL for pile {pile_id}: {e}")

        return exported_files
