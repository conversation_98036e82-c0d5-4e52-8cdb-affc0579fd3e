"""
Boundary Clipper Module for 3D Pile Volume Analysis

This module handles site boundary processing and clipping operations using
<PERSON><PERSON><PERSON><PERSON> for 2D operations, Trimesh for 3D boolean operations, and CadQuery
for 3D solid operations that maintain CAD compatibility.
"""

import trimesh
import numpy as np
import pandas as pd
from shapely.geometry import Polygon, Point
from shapely.ops import unary_union
from typing import Dict, List, Tuple, Optional, Union
import logging

try:
    import cadquery as cq
    CADQUERY_AVAILABLE = True
except ImportError:
    CADQUERY_AVAILABLE = False
    logging.warning("CadQuery not available - 3D solid clipping functionality disabled")

from .geometry_engine import PileGeometry

logger = logging.getLogger(__name__)


class BoundaryClipper:
    """
    Handles site boundary processing and 3D geometry clipping operations.
    """
    
    def __init__(self, boundary_coords: List[Tuple[float, float]], 
                 elevation_range: Optional[Tuple[float, float]] = None):
        """
        Initialize the BoundaryClipper.
        
        Args:
            boundary_coords: List of (x, y) coordinates defining site boundary
            elevation_range: Optional (min_z, max_z) for vertical clipping
        """
        self.boundary_coords = boundary_coords
        self.boundary_polygon = Polygon(boundary_coords)
        self.elevation_range = elevation_range
        
        # Validate boundary
        if not self.boundary_polygon.is_valid:
            raise ValueError("Invalid boundary polygon")
            
        logger.info(f"Initialized boundary clipper with {len(boundary_coords)} vertices")
        
    def create_boundary_mesh(self, height: float, base_elevation: float = 0.0) -> trimesh.Trimesh:
        """
        Create a 3D mesh representing the site boundary volume.
        
        Args:
            height: Height of the boundary volume
            base_elevation: Base elevation of the boundary
            
        Returns:
            3D mesh of the boundary volume
        """
        try:
            # Get boundary coordinates
            coords = list(self.boundary_polygon.exterior.coords[:-1])  # Remove duplicate last point
            
            # Create vertices for bottom and top faces
            bottom_vertices = [(x, y, base_elevation) for x, y in coords]
            top_vertices = [(x, y, base_elevation + height) for x, y in coords]
            
            # Combine all vertices
            vertices = np.array(bottom_vertices + top_vertices)
            
            # Create faces
            faces = []
            n = len(coords)
            
            # Bottom face (triangulate polygon)
            bottom_faces = self._triangulate_polygon(coords, offset=0)
            faces.extend(bottom_faces)
            
            # Top face (triangulate polygon, reverse winding)
            top_faces = self._triangulate_polygon(coords, offset=n, reverse=True)
            faces.extend(top_faces)
            
            # Side faces
            for i in range(n):
                next_i = (i + 1) % n
                # Two triangles per side face
                faces.append([i, next_i, n + i])
                faces.append([next_i, n + next_i, n + i])
                
            boundary_mesh = trimesh.Trimesh(vertices=vertices, faces=faces)
            
            if not boundary_mesh.is_watertight:
                logger.warning("Boundary mesh is not watertight")
                
            return boundary_mesh
            
        except Exception as e:
            logger.error(f"Error creating boundary mesh: {e}")
            raise
            
    def _triangulate_polygon(self, coords: List[Tuple[float, float]], 
                           offset: int = 0, reverse: bool = False) -> List[List[int]]:
        """
        Triangulate a polygon using ear clipping algorithm.
        
        Args:
            coords: List of polygon coordinates
            offset: Vertex index offset
            reverse: Whether to reverse triangle winding
            
        Returns:
            List of triangle face indices
        """
        n = len(coords)
        if n < 3:
            return []
            
        # Simple fan triangulation for convex polygons
        # For complex polygons, would need proper ear clipping
        faces = []
        for i in range(1, n - 1):
            if reverse:
                faces.append([offset, offset + i + 1, offset + i])
            else:
                faces.append([offset, offset + i, offset + i + 1])
                
        return faces
        
    def clip_geometry_to_boundary(self, geometry: PileGeometry, 
                                 boundary_mesh: trimesh.Trimesh) -> PileGeometry:
        """
        Clip pile geometry to site boundary.
        
        Args:
            geometry: Original pile geometry
            boundary_mesh: Site boundary mesh
            
        Returns:
            Clipped pile geometry
        """
        try:
            # Clip each component
            clipped_part1 = self._clip_mesh(geometry.part1_cylinder, boundary_mesh)
            clipped_part2 = self._clip_mesh(geometry.part2_frustum, boundary_mesh)
            clipped_part3 = self._clip_mesh(geometry.part3_cylinder, boundary_mesh)
            
            # Create clipped combined mesh
            clipped_combined = self._clip_mesh(geometry.combined_mesh, boundary_mesh)
            
            return PileGeometry(
                pile_id=geometry.pile_id,
                part1_cylinder=clipped_part1,
                part2_frustum=clipped_part2,
                part3_cylinder=clipped_part3,
                combined_mesh=clipped_combined
            )
            
        except Exception as e:
            logger.error(f"Error clipping geometry for pile {geometry.pile_id}: {e}")
            # Return original geometry if clipping fails
            return geometry
            
    def _clip_mesh(self, mesh: trimesh.Trimesh, boundary_mesh: trimesh.Trimesh) -> trimesh.Trimesh:
        """
        Clip a mesh to boundary using boolean intersection.
        
        Args:
            mesh: Mesh to clip
            boundary_mesh: Boundary mesh
            
        Returns:
            Clipped mesh
        """
        try:
            # Perform boolean intersection
            clipped = mesh.intersection(boundary_mesh)
            
            # Validate result
            if clipped.vertices is None or len(clipped.vertices) == 0:
                logger.warning("Clipping resulted in empty mesh")
                return mesh  # Return original if clipping fails
                
            return clipped
            
        except Exception as e:
            logger.warning(f"Mesh clipping failed: {e}")
            return mesh  # Return original mesh
            
    def check_pile_in_boundary(self, pile_x: float, pile_y: float) -> bool:
        """
        Check if a pile center is within the site boundary.
        
        Args:
            pile_x: Pile X coordinate
            pile_y: Pile Y coordinate
            
        Returns:
            True if pile is within boundary
        """
        point = Point(pile_x, pile_y)
        return self.boundary_polygon.contains(point)
        
    def get_boundary_intersection_area(self, pile_x: float, pile_y: float, 
                                     pile_radius: float) -> float:
        """
        Calculate the area of intersection between pile footprint and boundary.
        
        Args:
            pile_x: Pile X coordinate
            pile_y: Pile Y coordinate
            pile_radius: Pile radius
            
        Returns:
            Intersection area
        """
        try:
            # Create pile footprint circle
            pile_circle = Point(pile_x, pile_y).buffer(pile_radius)
            
            # Calculate intersection
            intersection = self.boundary_polygon.intersection(pile_circle)
            
            return float(intersection.area)
            
        except Exception as e:
            logger.warning(f"Error calculating boundary intersection area: {e}")
            return 0.0
            
    def clip_all_geometries(self, geometries: Dict[str, PileGeometry]) -> Dict[str, PileGeometry]:
        """
        Clip all pile geometries to site boundary.
        
        Args:
            geometries: Dictionary of pile geometries
            
        Returns:
            Dictionary of clipped geometries
        """
        if not geometries:
            return {}
            
        # Determine elevation range from geometries
        all_elevations = []
        for geom in geometries.values():
            bounds = geom.combined_mesh.bounds
            if len(bounds) >= 6:  # 3D bounds: [min_x, min_y, min_z, max_x, max_y, max_z]
                all_elevations.extend([bounds[2], bounds[5]])  # min_z, max_z
            elif len(bounds) >= 4:  # 2D bounds: [min_x, min_y, max_x, max_y]
                # Use mesh vertices to get z-bounds
                vertices = geom.combined_mesh.vertices
                if vertices.shape[1] >= 3:  # Has z-coordinates
                    all_elevations.extend([vertices[:, 2].min(), vertices[:, 2].max()])
                else:
                    # Default elevation range if no z-coordinates
                    all_elevations.extend([0, 10])
            else:
                # Fallback for unexpected bounds format
                all_elevations.extend([0, 10])
            
        min_elev = min(all_elevations) - 10  # Add buffer
        max_elev = max(all_elevations) + 10
        height = max_elev - min_elev
        
        # Create boundary mesh
        boundary_mesh = self.create_boundary_mesh(height, min_elev)
        
        # Clip all geometries
        clipped_geometries = {}
        for pile_id, geometry in geometries.items():
            clipped_geometries[pile_id] = self.clip_geometry_to_boundary(geometry, boundary_mesh)
            
        logger.info(f"Clipped {len(clipped_geometries)} pile geometries to boundary")
        return clipped_geometries

    def create_boundary_solid(self, height: float, base_elevation: float = 0.0) -> Optional['cq.Workplane']:
        """
        Create a CadQuery solid representing the site boundary volume.

        Args:
            height: Height of the boundary volume
            base_elevation: Base elevation of the boundary

        Returns:
            CadQuery solid of the boundary volume, or None if CadQuery not available
        """
        if not CADQUERY_AVAILABLE:
            logger.warning("CadQuery not available - cannot create boundary solid")
            return None

        try:
            # Get boundary coordinates
            coords = list(self.boundary_polygon.exterior.coords[:-1])  # Remove duplicate last point

            # Create 2D polygon in CadQuery
            points = [(x, y) for x, y in coords]

            # Create extruded solid from polygon
            boundary_solid = (cq.Workplane("XY")
                            .polyline(points)
                            .close()
                            .extrude(height)
                            .translate((0, 0, base_elevation)))

            logger.debug(f"Created CadQuery boundary solid with height {height} at elevation {base_elevation}")
            return boundary_solid

        except Exception as e:
            logger.error(f"Error creating CadQuery boundary solid: {e}")
            return None

    def clip_cadquery_solid_to_boundary(self, solid: 'cq.Workplane',
                                       boundary_solid: 'cq.Workplane') -> Optional['cq.Workplane']:
        """
        Clip a CadQuery solid to site boundary using boolean intersection.

        Args:
            solid: CadQuery solid to clip
            boundary_solid: Site boundary solid

        Returns:
            Clipped CadQuery solid, or None if clipping fails
        """
        if not CADQUERY_AVAILABLE:
            return None

        try:
            # Perform boolean intersection
            clipped_solid = solid.intersect(boundary_solid)

            logger.debug("Successfully clipped CadQuery solid to boundary")
            return clipped_solid

        except Exception as e:
            logger.warning(f"CadQuery solid clipping failed: {e}")
            return None

    def clip_geometry_to_boundary_with_solids(self, geometry: PileGeometry,
                                            boundary_solid: 'cq.Workplane') -> PileGeometry:
        """
        Clip pile geometry to site boundary, maintaining both Trimesh and CadQuery representations.

        Args:
            geometry: Original pile geometry
            boundary_solid: Site boundary CadQuery solid

        Returns:
            Clipped pile geometry with both mesh and solid representations
        """
        try:
            # First perform standard Trimesh clipping
            boundary_mesh = self._cadquery_solid_to_trimesh(boundary_solid)
            if boundary_mesh is not None:
                clipped_geometry = self.clip_geometry_to_boundary(geometry, boundary_mesh)
            else:
                clipped_geometry = geometry

            # TODO: Add CadQuery solid clipping when geometry engine supports CadQuery solids
            # This would require extending PileGeometry to include CadQuery solid representations

            return clipped_geometry

        except Exception as e:
            logger.error(f"Error clipping geometry with solids for pile {geometry.pile_id}: {e}")
            return geometry

    def _cadquery_solid_to_trimesh(self, solid: 'cq.Workplane') -> Optional[trimesh.Trimesh]:
        """
        Convert a CadQuery solid to Trimesh for compatibility with existing code.

        Args:
            solid: CadQuery solid to convert

        Returns:
            Trimesh object, or None if conversion fails
        """
        if not CADQUERY_AVAILABLE:
            return None

        try:
            # Export CadQuery solid to STL and import as Trimesh
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(suffix='.stl', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # Export solid to temporary STL file
                cq.exporters.export(solid, temp_path)

                # Import STL as Trimesh
                mesh = trimesh.load(temp_path)

                return mesh

            finally:
                # Clean up temporary file
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

        except Exception as e:
            logger.warning(f"Error converting CadQuery solid to Trimesh: {e}")
            return None

    def clip_all_geometries_with_solids(self, geometries: Dict[str, PileGeometry]) -> Dict[str, PileGeometry]:
        """
        Clip all pile geometries to site boundary using CadQuery solids when available.

        Args:
            geometries: Dictionary of pile geometries

        Returns:
            Dictionary of clipped geometries
        """
        if not geometries:
            return {}

        # Determine elevation range from geometries
        all_elevations = []
        for geom in geometries.values():
            bounds = geom.combined_mesh.bounds
            if len(bounds) >= 6:  # 3D bounds: [min_x, min_y, min_z, max_x, max_y, max_z]
                all_elevations.extend([bounds[2], bounds[5]])  # min_z, max_z
            elif len(bounds) >= 4:  # 2D bounds: [min_x, min_y, max_x, max_y]
                # Use mesh vertices to get z-bounds
                vertices = geom.combined_mesh.vertices
                if vertices.shape[1] >= 3:  # Has z-coordinates
                    all_elevations.extend([vertices[:, 2].min(), vertices[:, 2].max()])
                else:
                    # Default elevation range if no z-coordinates
                    all_elevations.extend([0, 10])
            else:
                # Fallback for unexpected bounds format
                all_elevations.extend([0, 10])

        min_elev = min(all_elevations) - 10  # Add buffer
        max_elev = max(all_elevations) + 10
        height = max_elev - min_elev

        # Create boundary solid if CadQuery is available
        boundary_solid = None
        if CADQUERY_AVAILABLE:
            boundary_solid = self.create_boundary_solid(height, min_elev)

        # Clip all geometries
        clipped_geometries = {}
        for pile_id, geometry in geometries.items():
            if boundary_solid is not None:
                clipped_geometries[pile_id] = self.clip_geometry_to_boundary_with_solids(
                    geometry, boundary_solid
                )
            else:
                # Fallback to standard mesh clipping
                boundary_mesh = self.create_boundary_mesh(height, min_elev)
                clipped_geometries[pile_id] = self.clip_geometry_to_boundary(geometry, boundary_mesh)

        logger.info(f"Clipped {len(clipped_geometries)} pile geometries to boundary using {'CadQuery solids' if boundary_solid else 'Trimesh'}")
        return clipped_geometries

    def get_boundary_info(self) -> Dict[str, Union[float, List[Tuple[float, float]]]]:
        """
        Get information about the site boundary.

        Returns:
            Dictionary with boundary properties
        """
        return {
            'area': float(self.boundary_polygon.area),
            'perimeter': float(self.boundary_polygon.length),
            'bounds': list(self.boundary_polygon.bounds),  # (minx, miny, maxx, maxy)
            'coordinates': self.boundary_coords,
            'is_valid': self.boundary_polygon.is_valid,
            'is_simple': self.boundary_polygon.is_simple,
            'cadquery_available': CADQUERY_AVAILABLE
        }
