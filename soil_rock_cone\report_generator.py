"""
Report Generator Module for 3D Pile Volume Analysis

This module handles generation of detailed reports including individual pile
volume reports and overlap analysis reports in the exact format specified
in the requirements.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from pathlib import Path
from datetime import datetime

try:
    from .volume_calculator import VolumeResult
    from .overlap_analyzer import OverlapResult
except ImportError:
    # For standalone testing, create mock classes
    class VolumeResult:
        pass
    class OverlapResult:
        pass

logger = logging.getLogger(__name__)


class ReportGenerator:
    """
    Handles generation of detailed reports for pile volume analysis.
    """
    
    def __init__(self):
        """Initialize the ReportGenerator."""
        pass
        
    def generate_individual_pile_report(self,
                                      pile_data: pd.DataFrame,
                                      volume_results: Dict[str, VolumeResult],
                                      overlap_results: Optional[Dict] = None) -> pd.DataFrame:
        """
        Generate individual pile volume report in the exact format specified in Section 7.1.

        Creates comprehensive DataFrame with all required columns:
        - Primary Volume Components
        - Site Boundary Clipping Analysis
        - Non-Overlapping Volume Components
        - Overlap Analysis Statistics
        - Shared Volume Allocations
        - Final Volume Summary

        Args:
            pile_data: Original pile data with calculated properties
            volume_results: Volume calculation results from GeometryEngine
            overlap_results: Optional overlap analysis results from OverlapAnalyzer

        Returns:
            DataFrame with specification-compliant individual pile volume report
        """
        report_data = []

        for _, pile in pile_data.iterrows():
            pile_id = pile['pile_id']

            # Get volume results
            if pile_id in volume_results:
                vol_result = volume_results[pile_id]
            else:
                logger.warning(f"No volume results found for pile {pile_id}")
                continue

            # Get overlap statistics for this pile
            overlap_stats = self._get_pile_overlap_stats(pile_id, overlap_results) if overlap_results else {}

            # Create report row according to Section 7.1 specification
            report_row = {
                # Primary Volume Components
                'pile_mark': pile_id,
                'original_part1_volume': getattr(vol_result, 'original_part1_volume', 0.0),
                'original_part2_volume': getattr(vol_result, 'original_part2_volume', 0.0),
                'original_part3_volume': getattr(vol_result, 'original_part3_volume', 0.0),
                'material_classification': pile['material_type'],

                # Site Boundary Clipping Analysis
                'clipped_part1_volume': getattr(vol_result, 'clipped_part1_volume', 0.0),
                'clipped_part2_volume': getattr(vol_result, 'clipped_part2_volume', 0.0),
                'clipped_part3_volume': getattr(vol_result, 'clipped_part3_volume', 0.0),
                'part1_boundary_loss': getattr(vol_result, 'part1_boundary_loss', 0.0),
                'part2_boundary_loss': getattr(vol_result, 'part2_boundary_loss', 0.0),
                'part3_boundary_loss': getattr(vol_result, 'part3_boundary_loss', 0.0),
                'part1_boundary_compliance_pct': self._calculate_compliance_percent(
                    getattr(vol_result, 'clipped_part1_volume', 0.0),
                    getattr(vol_result, 'original_part1_volume', 0.0)
                ),
                'part2_boundary_compliance_pct': self._calculate_compliance_percent(
                    getattr(vol_result, 'clipped_part2_volume', 0.0),
                    getattr(vol_result, 'original_part2_volume', 0.0)
                ),
                'part3_boundary_compliance_pct': self._calculate_compliance_percent(
                    getattr(vol_result, 'clipped_part3_volume', 0.0),
                    getattr(vol_result, 'original_part3_volume', 0.0)
                ),

                # Non-Overlapping Volume Components
                'isolated_part1_volume': getattr(vol_result, 'isolated_part1_volume', 0.0),
                'isolated_part2_volume': getattr(vol_result, 'isolated_part2_volume', 0.0),
                'isolated_part3_volume': getattr(vol_result, 'isolated_part3_volume', 0.0),

                # Overlap Analysis Statistics
                'part2_2_overlap_count': overlap_stats.get('part2_2_count', 0),
                'part2_3_overlap_count': overlap_stats.get('part2_3_count', 0),
                'part3_3_overlap_count': overlap_stats.get('part3_3_count', 0),
                'total_overlap_count': overlap_stats.get('total_count', 0),

                # Shared Volume Allocations
                'shared_part2_2_volume': overlap_stats.get('shared_part2_2_volume', 0.0),
                'shared_part2_3_volume': overlap_stats.get('shared_part2_3_volume', 0.0),
                'shared_part3_3_volume': overlap_stats.get('shared_part3_3_volume', 0.0),
                'total_shared_volume': overlap_stats.get('total_shared_volume', 0.0),

                # Final Volume Summary
                'total_soil_volume': self._calculate_total_soil_volume(vol_result, overlap_stats, pile['material_type']),
                'total_rock_volume': self._calculate_total_rock_volume(vol_result, overlap_stats, pile['material_type']),
                'total_volume': self._calculate_total_volume(vol_result, overlap_stats)
            }
            
            report_data.append(report_row)
            
        df = pd.DataFrame(report_data)
        logger.info(f"Generated individual pile report with {len(df)} piles")
        
        return df
        
    def generate_overlap_analysis_report(self,
                                       overlap_results: List[OverlapResult]) -> pd.DataFrame:
        """
        Generate overlap analysis report in the exact format specified in Section 7.2.

        Creates comprehensive DataFrame with all required columns:
        - Overlap Identification
        - Detailed Component Analysis
        - Volume Information
        - Location Data
        - Quality Checks

        Args:
            overlap_results: List of overlap analysis results from OverlapAnalyzer

        Returns:
            DataFrame with specification-compliant overlap analysis report
        """
        if not overlap_results:
            # Return empty DataFrame with specification-compliant columns
            return pd.DataFrame(columns=[
                'overlap_code', 'overlap_type', 'contributing_pile_marks', 'pile_count',
                'pile_part_combinations', 'contributing_pile_parts', 'overlap_volume',
                'volume_per_pile', 'dominant_material_type', 'centroid_x', 'centroid_y',
                'centroid_z', 'within_site_boundary', 'geometric_validity'
            ])

        report_data = []
        overlap_counter = {'Part2-2': 1, 'Part2-3': 1, 'Part3-3': 1}

        for overlap in overlap_results:
            # Generate overlap code
            overlap_type = overlap.overlap_type
            overlap_code = f"OVL-P{overlap_type.replace('-', '')}-{overlap_counter[overlap_type]:03d}"
            overlap_counter[overlap_type] += 1

            # Get contributing piles
            contributing_piles = overlap.contributing_piles
            pile_count = len(contributing_piles)

            # Create pile part combinations and contributing pile parts
            pile_part_combinations = self._create_pile_part_combinations(overlap)
            contributing_pile_parts = self._create_contributing_pile_parts(overlap)

            # Calculate volume per pile
            volume_per_pile = overlap.overlap_volume / pile_count if pile_count > 0 else 0.0

            # Determine dominant material type
            dominant_material = self._determine_dominant_material(overlap)

            report_row = {
                # Overlap Identification
                'overlap_code': overlap_code,
                'overlap_type': overlap_type,
                'contributing_pile_marks': ';'.join(contributing_piles),
                'pile_count': pile_count,

                # Detailed Component Analysis
                'pile_part_combinations': pile_part_combinations,
                'contributing_pile_parts': contributing_pile_parts,

                # Volume Information
                'overlap_volume': round(overlap.overlap_volume, 4),
                'volume_per_pile': round(volume_per_pile, 4),
                'dominant_material_type': dominant_material,

                # Location Data
                'centroid_x': round(getattr(overlap, 'centroid_x', 0.0), 2),
                'centroid_y': round(getattr(overlap, 'centroid_y', 0.0), 2),
                'centroid_z': round(getattr(overlap, 'centroid_z', 0.0), 2),

                # Quality Checks
                'within_site_boundary': getattr(overlap, 'within_site_boundary', True),
                'geometric_validity': getattr(overlap, 'geometric_validity', True)
            }

            report_data.append(report_row)

        df = pd.DataFrame(report_data)
        logger.info(f"Generated overlap analysis report with {len(df)} overlaps")

        return df
        
    def generate_summary_report(self, 
                              pile_data: pd.DataFrame,
                              volume_results: Dict[str, VolumeResult],
                              overlap_volumes: Dict[Tuple[str, str], float],
                              boundary_info: Dict) -> Dict[str, Union[int, float, str]]:
        """
        Generate summary statistics report.
        
        Args:
            pile_data: Original pile data
            volume_results: Volume calculation results
            overlap_volumes: Overlap volume data
            boundary_info: Site boundary information
            
        Returns:
            Dictionary with summary statistics
        """
        if not volume_results:
            return {}
            
        # Extract volume data
        total_volumes = [r.total_volume for r in volume_results.values()]
        final_volumes = [r.final_volume for r in volume_results.values()]
        overlap_volume_list = [r.overlap_volume for r in volume_results.values()]
        
        summary = {
            # Project Information
            'Analysis_Date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'Total_Piles_Analyzed': len(volume_results),
            'Site_Boundary_Area': boundary_info.get('area', 0.0),
            'Site_Boundary_Perimeter': boundary_info.get('perimeter', 0.0),
            
            # Volume Statistics
            'Total_Theoretical_Volume': sum(total_volumes),
            'Total_Final_Volume': sum(final_volumes),
            'Total_Volume_Lost_to_Overlaps': sum(overlap_volume_list),
            'Average_Pile_Volume': np.mean(total_volumes),
            'Maximum_Pile_Volume': max(total_volumes) if total_volumes else 0.0,
            'Minimum_Pile_Volume': min(total_volumes) if total_volumes else 0.0,
            'Volume_Standard_Deviation': np.std(total_volumes),
            
            # Efficiency Metrics
            'Overall_Volume_Efficiency': sum(final_volumes) / sum(total_volumes) if sum(total_volumes) > 0 else 0.0,
            'Average_Overlap_Loss_Percentage': np.mean([
                r.overlap_volume / r.total_volume * 100 if r.total_volume > 0 else 0.0
                for r in volume_results.values()
            ]),
            
            # Overlap Statistics
            'Total_Overlapping_Pairs': len(overlap_volumes),
            'Total_Overlap_Volume': sum(overlap_volumes.values()),
            'Average_Overlap_Volume': np.mean(list(overlap_volumes.values())) if overlap_volumes else 0.0,
            'Maximum_Overlap_Volume': max(overlap_volumes.values()) if overlap_volumes else 0.0,
            
            # Material Type Breakdown
            'Soil_Piles_Count': len(pile_data[pile_data['material_type'].str.lower() == 'soil']),
            'Rock_Piles_Count': len(pile_data[pile_data['material_type'].str.lower() == 'rock']),
        }
        
        return summary
        
    def _calculate_error_percent(self, calculated: float, theoretical: float) -> float:
        """Calculate percentage error between calculated and theoretical values."""
        if theoretical == 0:
            return 0.0 if calculated == 0 else float('inf')
        return abs(calculated - theoretical) / theoretical * 100
        
    def _calculate_efficiency(self, actual: float, total: float) -> float:
        """Calculate efficiency as percentage."""
        if total == 0:
            return 100.0 if actual == 0 else 0.0
        return actual / total * 100
        
    def _calculate_percentage(self, part: float, total: float) -> float:
        """Calculate percentage."""
        if total == 0:
            return 0.0
        return part / total * 100
        
    def export_reports_to_csv(self,
                            individual_report: pd.DataFrame,
                            overlap_report: pd.DataFrame,
                            output_dir: Union[str, Path] = "output") -> Dict[str, str]:
        """
        Export reports to CSV files with specification-compliant naming convention.

        Uses naming convention from Section 7.1 and 7.2:
        - Individual pile report: cone_volume_analysis_[timestamp].csv
        - Overlap analysis report: overlap_analysis_[timestamp].csv

        Args:
            individual_report: Individual pile volume report
            overlap_report: Overlap analysis report
            output_dir: Output directory path

        Returns:
            Dictionary mapping report type to file path
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Export individual pile report with specification-compliant naming
        individual_file = output_path / f"cone_volume_analysis_{timestamp}.csv"
        individual_report.round(3).to_csv(individual_file, index=False, encoding='utf-8')

        # Export overlap report with specification-compliant naming
        overlap_file = output_path / f"overlap_analysis_{timestamp}.csv"
        overlap_report.to_csv(overlap_file, index=False, encoding='utf-8')

        file_paths = {
            'individual_report': str(individual_file),
            'overlap_report': str(overlap_file)
        }
        
        logger.info(f"Exported reports to {output_dir}")
        return file_paths

    def _get_pile_overlap_stats(self, pile_id: str, overlap_results: Dict) -> Dict:
        """Get overlap statistics for a specific pile."""
        if not overlap_results:
            return {}

        stats = {
            'part2_2_count': 0,
            'part2_3_count': 0,
            'part3_3_count': 0,
            'total_count': 0,
            'shared_part2_2_volume': 0.0,
            'shared_part2_3_volume': 0.0,
            'shared_part3_3_volume': 0.0,
            'total_shared_volume': 0.0
        }

        # Count overlaps and sum volumes for this pile
        for overlap in overlap_results.get('overlaps', []):
            if pile_id in overlap.contributing_piles:
                overlap_type = overlap.overlap_type
                volume_per_pile = overlap.overlap_volume / len(overlap.contributing_piles)

                if overlap_type == 'Part2-2':
                    stats['part2_2_count'] += 1
                    stats['shared_part2_2_volume'] += volume_per_pile
                elif overlap_type == 'Part2-3':
                    stats['part2_3_count'] += 1
                    stats['shared_part2_3_volume'] += volume_per_pile
                elif overlap_type == 'Part3-3':
                    stats['part3_3_count'] += 1
                    stats['shared_part3_3_volume'] += volume_per_pile

                stats['total_count'] += 1

        stats['total_shared_volume'] = (
            stats['shared_part2_2_volume'] +
            stats['shared_part2_3_volume'] +
            stats['shared_part3_3_volume']
        )

        return stats

    def _calculate_compliance_percent(self, clipped_volume: float, original_volume: float) -> float:
        """Calculate boundary compliance percentage."""
        if original_volume == 0:
            return 100.0 if clipped_volume == 0 else 0.0
        return (clipped_volume / original_volume) * 100

    def _calculate_total_soil_volume(self, vol_result, overlap_stats: Dict, material_type: str) -> float:
        """Calculate total soil volume for a pile."""
        soil_volume = 0.0

        # Add isolated volumes if material is Soil
        if material_type == 'Soil':
            soil_volume += getattr(vol_result, 'isolated_part2_volume', 0.0)

        # Always add Part 3 (soil cylinder) volume
        soil_volume += getattr(vol_result, 'isolated_part3_volume', 0.0)

        # Add shared soil volumes
        soil_volume += overlap_stats.get('shared_part2_2_volume', 0.0) if material_type == 'Soil' else 0.0
        soil_volume += overlap_stats.get('shared_part2_3_volume', 0.0)
        soil_volume += overlap_stats.get('shared_part3_3_volume', 0.0)

        return soil_volume

    def _calculate_total_rock_volume(self, vol_result, overlap_stats: Dict, material_type: str) -> float:
        """Calculate total rock volume for a pile."""
        rock_volume = 0.0

        # Add isolated volumes if material is Rock
        if material_type == 'Rock':
            rock_volume += getattr(vol_result, 'isolated_part2_volume', 0.0)
            rock_volume += overlap_stats.get('shared_part2_2_volume', 0.0)

        return rock_volume

    def _calculate_total_volume(self, vol_result, overlap_stats: Dict) -> float:
        """Calculate total volume for a pile."""
        total_volume = 0.0

        # Add all isolated volumes
        total_volume += getattr(vol_result, 'isolated_part1_volume', 0.0)
        total_volume += getattr(vol_result, 'isolated_part2_volume', 0.0)
        total_volume += getattr(vol_result, 'isolated_part3_volume', 0.0)

        # Add all shared volumes
        total_volume += overlap_stats.get('total_shared_volume', 0.0)

        return total_volume

    def _create_pile_part_combinations(self, overlap) -> str:
        """Create pile part combinations string."""
        # This would need to be implemented based on the overlap structure
        return f"Pile-{overlap.contributing_piles[0]}:Part{overlap.overlap_type.split('-')[0]} x Pile-{overlap.contributing_piles[1]}:Part{overlap.overlap_type.split('-')[1]}"

    def _create_contributing_pile_parts(self, overlap) -> str:
        """Create contributing pile parts string."""
        parts = []
        for pile in overlap.contributing_piles:
            part_type = overlap.overlap_type.split('-')[0] if pile == overlap.contributing_piles[0] else overlap.overlap_type.split('-')[1]
            material = getattr(overlap, 'material_type', 'Soil')
            parts.append(f"Pile-{pile}:Part{part_type}:{material}")
        return ';'.join(parts)

    def _determine_dominant_material(self, overlap) -> str:
        """Determine dominant material type for overlap."""
        # This would need to be implemented based on the overlap structure
        return getattr(overlap, 'dominant_material_type', 'Soil')
