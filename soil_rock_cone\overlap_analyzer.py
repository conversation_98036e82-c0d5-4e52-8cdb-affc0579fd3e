"""
Overlap Analyzer Module for 3D Pile Volume Analysis

This module handles detection and analysis of overlapping volumes between
pile geometries using spatial indexing and boolean operations.
"""

import trimesh
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Set
import logging
from dataclasses import dataclass
from scipy.spatial import cKDTree

from .geometry_engine import PileGeometry

logger = logging.getLogger(__name__)


@dataclass
class OverlapResult:
    """
    Container for overlap analysis results - specification compliant.

    Supports only the three overlap types specified in Section 3.5.1:
    - Part2-2: Intersection between soil/rock frustums of different piles
    - Part2-3: Intersection between one pile's frustum and another pile's soil cylinder
    - Part3-3: Intersection between soil cylinders of different piles
    """
    contributing_piles: List[str]  # List of pile IDs involved in overlap
    overlap_volume: float  # Total intersection volume (m³)
    overlap_type: str  # 'Part2-2', 'Part2-3', 'Part3-3'
    overlap_mesh: Optional[trimesh.Trimesh] = None
    # Additional attributes for ReportGenerator compliance
    centroid_x: float = 0.0
    centroid_y: float = 0.0
    centroid_z: float = 0.0
    within_site_boundary: bool = True
    geometric_validity: bool = True
    dominant_material_type: str = 'Soil'


class OverlapAnalyzer:
    """
    Handles detection and analysis of overlapping volumes between pile geometries.

    Implements specification-compliant overlap detection per Section 3.5.1:
    - Only detects Part 2-2, Part 2-3, and Part 3-3 overlaps
    - Uses minimum volume threshold of 0.001 m³
    - Implements spatial indexing for performance
    - Ensures geometric validation and site boundary compliance
    """

    def __init__(self, minimum_volume_threshold: float = 0.001, spatial_tolerance: float = 0.1):
        """
        Initialize the OverlapAnalyzer with specification-compliant parameters.

        Args:
            minimum_volume_threshold: Minimum overlap volume to consider (0.001 m³ per spec)
            spatial_tolerance: Tolerance for spatial proximity checks in spatial indexing
        """
        self.minimum_volume_threshold = minimum_volume_threshold  # 0.001 m³ per specification
        self.spatial_tolerance = spatial_tolerance  # For spatial indexing only
        
    def find_potential_overlaps(self, geometries: Dict[str, PileGeometry]) -> List[Tuple[str, str]]:
        """
        Find pairs of piles that potentially overlap using spatial indexing.
        
        Args:
            geometries: Dictionary of pile geometries
            
        Returns:
            List of pile ID pairs that may overlap
        """
        if len(geometries) < 2:
            return []
            
        # Extract pile centers and bounding boxes
        pile_ids = list(geometries.keys())
        centers = []
        max_radii = []
        
        for pile_id in pile_ids:
            geometry = geometries[pile_id]
            bounds = geometry.combined_mesh.bounds

            # Handle different bounds formats
            if len(bounds) >= 6:  # 3D bounds: [min_x, min_y, min_z, max_x, max_y, max_z]
                center_x = (bounds[0] + bounds[3]) / 2
                center_y = (bounds[1] + bounds[4]) / 2
                center_z = (bounds[2] + bounds[5]) / 2
                centers.append([center_x, center_y, center_z])

                # Calculate maximum radius (conservative estimate)
                max_radius = max(
                    bounds[3] - bounds[0],  # x extent
                    bounds[4] - bounds[1],  # y extent
                    bounds[5] - bounds[2]   # z extent
                ) / 2
                max_radii.append(max_radius)

            elif len(bounds) >= 4:  # 2D bounds: [min_x, min_y, max_x, max_y]
                center_x = (bounds[0] + bounds[2]) / 2
                center_y = (bounds[1] + bounds[3]) / 2

                # Get z-bounds from vertices
                vertices = geometry.combined_mesh.vertices
                if vertices.shape[1] >= 3:
                    center_z = (vertices[:, 2].min() + vertices[:, 2].max()) / 2
                    z_extent = vertices[:, 2].max() - vertices[:, 2].min()
                else:
                    center_z = 0
                    z_extent = 0

                centers.append([center_x, center_y, center_z])

                # Calculate maximum radius
                max_radius = max(
                    bounds[2] - bounds[0],  # x extent
                    bounds[3] - bounds[1],  # y extent
                    z_extent                # z extent
                ) / 2
                max_radii.append(max_radius)

            else:
                # Fallback: use mesh vertices directly
                vertices = geometry.combined_mesh.vertices
                if len(vertices) > 0:
                    center = vertices.mean(axis=0)
                    if len(center) >= 3:
                        centers.append(center[:3])
                    else:
                        centers.append([center[0], center[1], 0])

                    # Calculate radius as maximum distance from center
                    distances = np.linalg.norm(vertices[:, :3] - center[:3], axis=1)
                    max_radii.append(distances.max())
                else:
                    centers.append([0, 0, 0])
                    max_radii.append(1.0)
            
        centers = np.array(centers)
        max_radii = np.array(max_radii)
        
        # Build spatial index
        tree = cKDTree(centers)
        
        # Find potential overlaps
        potential_pairs = []
        
        for i, pile_id1 in enumerate(pile_ids):
            # Query neighbors within potential overlap distance
            search_radius = max_radii[i] * 2 + self.spatial_tolerance
            neighbor_indices = tree.query_ball_point(centers[i], search_radius)
            
            for j in neighbor_indices:
                if j > i:  # Avoid duplicates and self-comparison
                    pile_id2 = pile_ids[j]
                    potential_pairs.append((pile_id1, pile_id2))
                    
        logger.info(f"Found {len(potential_pairs)} potential overlap pairs")
        return potential_pairs
        
    def analyze_overlap(self, geometry1: PileGeometry, geometry2: PileGeometry) -> List[OverlapResult]:
        """
        Analyze overlap between two pile geometries - specification compliant.

        Only detects the three overlap types specified in Section 3.5.1:
        - Part2-2: Intersection between soil/rock frustums of different piles
        - Part2-3: Intersection between one pile's frustum and another pile's soil cylinder
        - Part3-3: Intersection between soil cylinders of different piles

        Args:
            geometry1: First pile geometry
            geometry2: Second pile geometry

        Returns:
            List of overlap results for specification-compliant overlap types only
        """
        overlaps = []

        # Define ONLY the specification-compliant component pairs (Section 3.5.1)
        # Part 1 overlaps are explicitly excluded per specification
        component_pairs = [
            ('part2_frustum', 'part2_frustum', 'Part2-2'),  # Frustum-Frustum
            ('part2_frustum', 'part3_cylinder', 'Part2-3'),  # Frustum-Cylinder
            ('part3_cylinder', 'part3_cylinder', 'Part3-3'),  # Cylinder-Cylinder
        ]

        for comp1, comp2, overlap_type in component_pairs:
            try:
                mesh1 = getattr(geometry1, comp1, None)
                mesh2 = getattr(geometry2, comp2, None)

                # Skip if either mesh is missing
                if mesh1 is None or mesh2 is None:
                    continue

                # Calculate intersection
                overlap_volume, overlap_mesh = self._calculate_intersection(mesh1, mesh2)

                # Apply specification-compliant minimum volume threshold (0.001 m³)
                if overlap_volume > self.minimum_volume_threshold:
                    # Calculate centroid for location data
                    centroid = self._calculate_overlap_centroid(overlap_mesh) if overlap_mesh else (0.0, 0.0, 0.0)

                    # Determine dominant material type
                    dominant_material = self._determine_dominant_material_type(
                        geometry1, geometry2, comp1, comp2
                    )

                    overlaps.append(OverlapResult(
                        contributing_piles=[geometry1.pile_id, geometry2.pile_id],
                        overlap_volume=overlap_volume,
                        overlap_type=overlap_type,
                        overlap_mesh=overlap_mesh,
                        centroid_x=centroid[0],
                        centroid_y=centroid[1],
                        centroid_z=centroid[2],
                        within_site_boundary=True,  # Will be validated later
                        geometric_validity=self._validate_overlap_geometry(overlap_mesh),
                        dominant_material_type=dominant_material
                    ))

            except Exception as e:
                logger.warning(f"Error analyzing {overlap_type} overlap between "
                             f"{geometry1.pile_id} and {geometry2.pile_id}: {e}")

        return overlaps
        
    def _calculate_intersection(self, mesh1: trimesh.Trimesh, 
                              mesh2: trimesh.Trimesh) -> Tuple[float, Optional[trimesh.Trimesh]]:
        """
        Calculate intersection volume and mesh between two meshes.
        
        Args:
            mesh1: First mesh
            mesh2: Second mesh
            
        Returns:
            Tuple of (intersection_volume, intersection_mesh)
        """
        try:
            # Check if meshes are valid
            if not self._is_valid_mesh(mesh1) or not self._is_valid_mesh(mesh2):
                return 0.0, None
                
            # Quick bounding box check
            if not self._bounding_boxes_overlap(mesh1, mesh2):
                return 0.0, None
                
            # Calculate intersection
            intersection = mesh1.intersection(mesh2)
            
            if intersection is None or intersection.vertices is None or len(intersection.vertices) == 0:
                return 0.0, None
                
            # Calculate volume
            volume = self._calculate_mesh_volume(intersection)
            
            return volume, intersection if volume > 0 else None
            
        except Exception as e:
            logger.debug(f"Intersection calculation failed: {e}")
            return 0.0, None
            
    def _is_valid_mesh(self, mesh: trimesh.Trimesh) -> bool:
        """Check if mesh is valid for intersection calculations."""
        return (mesh is not None and 
                mesh.vertices is not None and 
                len(mesh.vertices) > 0 and
                mesh.faces is not None and 
                len(mesh.faces) > 0)
                
    def _bounding_boxes_overlap(self, mesh1: trimesh.Trimesh, mesh2: trimesh.Trimesh) -> bool:
        """Check if bounding boxes of two meshes overlap."""
        bounds1 = mesh1.bounds
        bounds2 = mesh2.bounds
        
        # Check overlap in each dimension
        return (bounds1[0] <= bounds2[3] and bounds2[0] <= bounds1[3] and  # x
                bounds1[1] <= bounds2[4] and bounds2[1] <= bounds1[4] and  # y
                bounds1[2] <= bounds2[5] and bounds2[2] <= bounds1[5])     # z
                
    def _calculate_mesh_volume(self, mesh: trimesh.Trimesh) -> float:
        """Calculate mesh volume with error handling."""
        try:
            if not mesh.is_watertight:
                logger.debug("Intersection mesh is not watertight")
                
            volume = float(mesh.volume)
            return abs(volume)  # Ensure positive volume
            
        except Exception as e:
            logger.debug(f"Error calculating intersection volume: {e}")
            return 0.0
            
    def analyze_all_overlaps(self, geometries: Dict[str, PileGeometry]) -> Dict[Tuple[str, str], float]:
        """
        Analyze overlaps between all pile pairs.
        
        Args:
            geometries: Dictionary of pile geometries
            
        Returns:
            Dictionary mapping pile pairs to total overlap volumes
        """
        # Find potential overlaps
        potential_pairs = self.find_potential_overlaps(geometries)
        
        # Analyze each potential overlap
        overlap_volumes = {}
        all_overlaps = []
        
        for pile1_id, pile2_id in potential_pairs:
            geometry1 = geometries[pile1_id]
            geometry2 = geometries[pile2_id]
            
            overlaps = self.analyze_overlap(geometry1, geometry2)
            all_overlaps.extend(overlaps)
            
            # Sum total overlap volume for this pair
            total_overlap = sum(overlap.overlap_volume for overlap in overlaps)
            
            if total_overlap > 0:
                overlap_volumes[(pile1_id, pile2_id)] = total_overlap
                
        logger.info(f"Found {len(overlap_volumes)} overlapping pile pairs with "
                   f"{len(all_overlaps)} component overlaps")
        
        return overlap_volumes
        
    def create_overlap_report(self, overlaps: List[OverlapResult]) -> pd.DataFrame:
        """
        Create a detailed overlap report - specification compliant.

        Args:
            overlaps: List of overlap results

        Returns:
            DataFrame with specification-compliant overlap details
        """
        if not overlaps:
            return pd.DataFrame(columns=[
                'contributing_piles', 'overlap_type', 'overlap_volume', 'centroid_x',
                'centroid_y', 'centroid_z', 'dominant_material_type'
            ])

        data = []
        for overlap in overlaps:
            data.append({
                'contributing_piles': ';'.join(overlap.contributing_piles),
                'overlap_type': overlap.overlap_type,
                'overlap_volume': overlap.overlap_volume,
                'centroid_x': overlap.centroid_x,
                'centroid_y': overlap.centroid_y,
                'centroid_z': overlap.centroid_z,
                'dominant_material_type': overlap.dominant_material_type,
                'within_site_boundary': overlap.within_site_boundary,
                'geometric_validity': overlap.geometric_validity
            })

        df = pd.DataFrame(data)

        # Add summary statistics
        logger.info(f"Overlap report created with {len(df)} entries")
        logger.info(f"Total overlap volume: {df['overlap_volume'].sum():.3f}")

        return df
        
    def get_overlap_statistics(self, overlap_volumes: Dict[Tuple[str, str], float]) -> Dict[str, float]:
        """
        Calculate overlap statistics.
        
        Args:
            overlap_volumes: Dictionary of overlap volumes
            
        Returns:
            Dictionary with overlap statistics
        """
        if not overlap_volumes:
            return {
                'total_overlaps': 0,
                'total_overlap_volume': 0.0,
                'average_overlap_volume': 0.0,
                'max_overlap_volume': 0.0,
                'min_overlap_volume': 0.0
            }
            
        volumes = list(overlap_volumes.values())
        
        return {
            'total_overlaps': len(overlap_volumes),
            'total_overlap_volume': sum(volumes),
            'average_overlap_volume': np.mean(volumes),
            'max_overlap_volume': max(volumes),
            'min_overlap_volume': min(volumes)
        }

    def _calculate_overlap_centroid(self, overlap_mesh: Optional[trimesh.Trimesh]) -> Tuple[float, float, float]:
        """Calculate centroid of overlap volume."""
        if overlap_mesh is None or overlap_mesh.vertices is None or len(overlap_mesh.vertices) == 0:
            return (0.0, 0.0, 0.0)

        try:
            centroid = overlap_mesh.centroid
            return (float(centroid[0]), float(centroid[1]), float(centroid[2]))
        except Exception:
            return (0.0, 0.0, 0.0)

    def _determine_dominant_material_type(self, geometry1: PileGeometry, geometry2: PileGeometry,
                                        comp1: str, comp2: str) -> str:
        """
        Determine dominant material type for overlap based on Part 2 components.

        Per specification Section 7.2:
        - "Soil", "Rock", or "Mixed" based on Part 2 components
        """
        materials = []

        # Get material types from pile geometries
        if hasattr(geometry1, 'material_type'):
            materials.append(geometry1.material_type)
        if hasattr(geometry2, 'material_type'):
            materials.append(geometry2.material_type)

        # Determine dominant material
        if not materials:
            return 'Soil'  # Default
        elif len(set(materials)) == 1:
            return materials[0]  # All same material
        else:
            return 'Mixed'  # Different materials

    def _validate_overlap_geometry(self, overlap_mesh: Optional[trimesh.Trimesh]) -> bool:
        """Validate that overlap geometry is watertight and valid."""
        if overlap_mesh is None:
            return False

        try:
            return overlap_mesh.is_watertight and overlap_mesh.is_valid
        except Exception:
            return False
