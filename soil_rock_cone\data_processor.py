"""
Data Processing Module for 3D Pile Volume Analysis

This module handles reading Excel data, validating inputs, and preparing
pile data for geometric analysis. Implements specification-compliant data processing
including concatenation from BP, SHP, DHP, MP sheets and Target Stratum parsing.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from pathlib import Path
import re

logger = logging.getLogger(__name__)


class DataProcessor:
    """
    Handles data processing for pile volume analysis including Excel reading,
    validation, and data preparation.

    Implements specification-compliant processing:
    - Concatenates data from BP, SHP, DHP, MP sheets into Pile data
    - Parses Target Stratum to determine material types
    - Handles Socket Length validation and processing
    - Determines Frustum Material based on Target Stratum and Socket Length
    """

    def __init__(self):
        """Initialize the DataProcessor."""
        self.required_columns = {
            'pile_id': str,
            'x_coord': float,
            'y_coord': float,
            'pile_cap_bottom_level': float,  # Pile Cap Bottom Level (mPD)
            'target_level': float,           # Frustum Top Level (mPD)
            'founding_level': float,         # Founding Level (mPD)
            'diameter': float,               # Pile Shaft Diameter (m)
            'material_type': str,            # Frustum Material ('Soil' or 'Rock')
            'target_stratum': str,           # Target Stratum (original field)
            'socket_length': float           # Socket Length (m)
        }
        
    def read_pile_data(self, excel_file: Union[str, Path],
                      file_paths: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """
        Read pile data from Excel file with specification-compliant concatenation.

        Concatenates data from BP, SHP, DHP, MP sheets into Pile data as specified.
        Implements Target Stratum parsing and Socket Length handling.

        Args:
            excel_file: Path to Excel file containing pile data
            file_paths: Optional dictionary of file paths (unused, for compatibility)

        Returns:
            DataFrame with standardized pile data

        Raises:
            FileNotFoundError: If Excel file doesn't exist
            ValueError: If required data is missing or invalid
        """
        try:
            logger.info(f"Reading pile data from {excel_file}")

            # Read and concatenate data from BP, SHP, DHP, MP sheets
            pile_data = self._concatenate_pile_sheets(excel_file)

            if pile_data.empty:
                raise ValueError("No pile data found in any of the required sheets (BP, SHP, DHP, MP)")

            # Standardize column names and process Target Stratum
            standardized_data = self._standardize_pile_data(pile_data)

            # Validate data integrity
            self._validate_pile_data(standardized_data)

            logger.info(f"Successfully loaded {len(standardized_data)} pile records")
            return standardized_data

        except Exception as e:
            logger.error(f"Error reading pile data from {excel_file}: {str(e)}")
            raise

    def read_site_boundary(self, excel_file: Union[str, Path]) -> List[Tuple[float, float]]:
        """
        Read site boundary data from Excel file SiteBoundary sheet.

        Args:
            excel_file: Path to Excel file containing site boundary data

        Returns:
            List of (x, y) coordinate tuples defining site boundary
        """
        try:
            logger.info(f"Reading site boundary from {excel_file}")

            # Read SiteBoundary sheet
            df_boundary = pd.read_excel(excel_file, sheet_name='SiteBoundary')

            # Validate required columns
            required_cols = ['X (m)', 'Y (m)']
            missing_cols = [col for col in required_cols if col not in df_boundary.columns]
            if missing_cols:
                raise ValueError(f"Missing required columns in SiteBoundary sheet: {missing_cols}")

            # Convert to list of tuples
            boundary_coords = [(row['X (m)'], row['Y (m)']) for _, row in df_boundary.iterrows()]

            # Validate boundary
            if len(boundary_coords) < 3:
                raise ValueError("Site boundary must have at least 3 points")

            # Auto-close boundary if needed
            if boundary_coords[0] != boundary_coords[-1]:
                boundary_coords.append(boundary_coords[0])
                logger.info("Auto-closed site boundary polygon")

            logger.info(f"Successfully loaded site boundary with {len(boundary_coords)} points")
            return boundary_coords

        except Exception as e:
            logger.error(f"Error reading site boundary from {excel_file}: {str(e)}")
            raise

    def _concatenate_pile_sheets(self, excel_file: Union[str, Path]) -> pd.DataFrame:
        """
        Concatenate data from BP, SHP, DHP, MP sheets into single DataFrame.

        Args:
            excel_file: Path to Excel file

        Returns:
            Concatenated DataFrame with all pile data
        """
        sheet_names = ['PileBP', 'PileSHP', 'PileDHP', 'PileMP']
        pile_dataframes = []

        for sheet_name in sheet_names:
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                if not df.empty:
                    # Add pile type column to track source sheet
                    df['pile_type'] = sheet_name.replace('Pile', '')
                    pile_dataframes.append(df)
                    logger.info(f"Loaded {len(df)} piles from {sheet_name}")
            except Exception as e:
                logger.warning(f"Could not read sheet {sheet_name}: {e}")
                continue

        if not pile_dataframes:
            return pd.DataFrame()

        # Concatenate all pile data
        concatenated = pd.concat(pile_dataframes, ignore_index=True, sort=False)
        logger.info(f"Concatenated {len(concatenated)} total piles from {len(pile_dataframes)} sheets")

        return concatenated

    def _standardize_pile_data(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """
        Standardize pile data column names and implement specification-compliant processing.

        Implements Target Stratum parsing and Socket Length handling as specified:
        - "Soil" when Target Stratum contains "Soil" OR Target Stratum contains "Rock" with Socket Length = 0
        - "Rock" when Target Stratum contains "Rock" with Socket Length > 0

        Args:
            raw_data: Raw DataFrame from Excel reading

        Returns:
            Standardized DataFrame with consistent column names and processed material types
        """
        # Create mapping for actual Excel column names
        column_mapping = {
            'pile mark': 'pile_id',
            'x (m)': 'x_coord',
            'y (m)': 'y_coord',
            'pile cap bottom level (mpd)': 'pile_cap_bottom_level',
            'target level (mpd)': 'target_level',
            'founding level (mpd)': 'founding_level',
            'pile shaft diameter (m)': 'diameter',
            'target stratum': 'target_stratum',
            'socket length (m)': 'socket_length'
        }

        # Apply column mapping
        standardized = raw_data.copy()
        standardized.columns = standardized.columns.str.lower().str.strip()

        for old_name, new_name in column_mapping.items():
            if old_name in standardized.columns:
                standardized = standardized.rename(columns={old_name: new_name})

        # Validate required fields are present
        required_fields = ['pile_id', 'x_coord', 'y_coord', 'pile_cap_bottom_level',
                          'target_level', 'founding_level', 'diameter', 'target_stratum', 'socket_length']
        missing_fields = [field for field in required_fields if field not in standardized.columns]
        if missing_fields:
            raise ValueError(f"Missing required fields: {missing_fields}")

        # Implement specification-compliant material type determination
        standardized['material_type'] = self._determine_material_type(
            standardized['target_stratum'],
            standardized['socket_length']
        )

        # Convert data types
        for col, dtype in self.required_columns.items():
            if col in standardized.columns:
                try:
                    if dtype == str:
                        standardized[col] = standardized[col].astype(str).str.strip()
                    else:
                        standardized[col] = pd.to_numeric(standardized[col], errors='coerce')
                except Exception as e:
                    logger.warning(f"Error converting column {col} to {dtype}: {e}")

        # Filter to only required columns and remove rows with missing critical data
        result = standardized[list(self.required_columns.keys())].copy()
        result = result.dropna(subset=['pile_id', 'x_coord', 'y_coord', 'pile_cap_bottom_level',
                                     'founding_level', 'diameter', 'target_stratum', 'socket_length'])

        logger.info(f"Standardized {len(result)} pile records with material types")
        return result

    def _determine_material_type(self, target_stratum_series: pd.Series,
                                socket_length_series: pd.Series) -> pd.Series:
        """
        Determine Frustum Material based on Target Stratum and Socket Length.

        Specification logic:
        - "Soil" when Target Stratum contains "Soil" OR Target Stratum contains "Rock" with Socket Length = 0
        - "Rock" when Target Stratum contains "Rock" with Socket Length > 0

        Args:
            target_stratum_series: Series containing Target Stratum values
            socket_length_series: Series containing Socket Length values

        Returns:
            Series with "Soil" or "Rock" material types
        """
        def determine_single_material(target_stratum, socket_length):
            target_str = str(target_stratum).strip()
            socket_len = float(socket_length) if pd.notna(socket_length) else 0.0

            # Check if Target Stratum contains "Soil"
            if 'Soil' in target_str:
                return 'Soil'

            # Check if Target Stratum contains "Rock"
            if 'Rock' in target_str:
                if socket_len == 0:
                    return 'Soil'  # Rock with Socket Length = 0 -> Soil
                else:
                    return 'Rock'  # Rock with Socket Length > 0 -> Rock

            # Default to Soil if neither condition is met
            logger.warning(f"Unclear Target Stratum '{target_str}', defaulting to 'Soil'")
            return 'Soil'

        # Apply the logic to each row
        material_types = []
        for target_stratum, socket_length in zip(target_stratum_series, socket_length_series):
            material_types.append(determine_single_material(target_stratum, socket_length))

        return pd.Series(material_types, index=target_stratum_series.index)
        
    def _validate_pile_data(self, data: pd.DataFrame) -> None:
        """
        Validate pile data for completeness and logical consistency.
        
        Args:
            data: Standardized pile DataFrame
            
        Raises:
            ValueError: If validation fails
        """
        # Check for missing values
        missing_data = data.isnull().sum()
        if missing_data.any():
            logger.warning(f"Missing data found: {missing_data[missing_data > 0].to_dict()}")
            
        # Check for duplicate pile IDs
        duplicates = data['pile_id'].duplicated().sum()
        if duplicates > 0:
            raise ValueError(f"Found {duplicates} duplicate pile IDs")
            
        # Validate elevation logic (pile_cap_bottom >= target >= founding)
        # Allow target_level = founding_level (no frustum case)
        invalid_elevations = (
            (data['pile_cap_bottom_level'] < data['target_level']) |
            (data['target_level'] < data['founding_level'])
        )
        if invalid_elevations.any():
            invalid_piles = data.loc[invalid_elevations, 'pile_id'].tolist()
            raise ValueError(f"Invalid elevation hierarchy (should be: founding_level ≤ target_level ≤ pile_cap_bottom_level) for piles: {invalid_piles}")
            
        # Validate positive diameters
        invalid_diameters = data['diameter'] <= 0
        if invalid_diameters.any():
            invalid_piles = data.loc[invalid_diameters, 'pile_id'].tolist()
            raise ValueError(f"Invalid diameters (<=0) for piles: {invalid_piles}")
            
        # Validate material types (specification uses 'Soil' and 'Rock')
        valid_materials = {'Soil', 'Rock'}
        invalid_materials = ~data['material_type'].isin(valid_materials)
        if invalid_materials.any():
            invalid_piles = data.loc[invalid_materials, 'pile_id'].tolist()
            raise ValueError(f"Invalid material types for piles: {invalid_piles}. Must be 'Soil' or 'Rock'")

        # Validate socket length is non-negative
        invalid_socket_lengths = data['socket_length'] < 0
        if invalid_socket_lengths.any():
            invalid_piles = data.loc[invalid_socket_lengths, 'pile_id'].tolist()
            raise ValueError(f"Invalid socket lengths (<0) for piles: {invalid_piles}")

        # Validate Target Stratum is not empty
        empty_target_stratum = data['target_stratum'].str.strip().eq('')
        if empty_target_stratum.any():
            invalid_piles = data.loc[empty_target_stratum, 'pile_id'].tolist()
            raise ValueError(f"Empty Target Stratum for piles: {invalid_piles}")
            
        logger.info("Pile data validation completed successfully")
        
    def calculate_pile_properties(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate additional pile properties needed for volume analysis.
        
        Args:
            data: Validated pile DataFrame
            
        Returns:
            DataFrame with additional calculated properties
        """
        result = data.copy()

        # Calculate pile dimensions
        result['pile_total_length'] = result['pile_cap_bottom_level'] - result['founding_level']
        result['frustum_height'] = result['target_level'] - result['founding_level']
        result['soil_cylinder_height'] = result['pile_cap_bottom_level'] - result['target_level']

        # Calculate pile radius (Part 1 - pile shaft)
        result['pile_radius'] = result['diameter'] / 2.0

        # Determine projection angle based on material type (specification uses 'Soil' and 'Rock')
        result['projection_angle'] = result['material_type'].map({
            'Soil': 15.0,  # degrees
            'Rock': 30.0   # degrees
        })

        # Calculate frustum top radius using projection angle (Part 2)
        # Formula: top_radius = base_radius + height * tan(angle)
        result['frustum_top_radius'] = (
            result['pile_radius'] +
            result['frustum_height'] * np.tan(np.radians(result['projection_angle']))
        )

        # Calculate volumes
        result['pile_cylinder_volume'] = np.pi * result['pile_radius']**2 * result['pile_total_length']
        result['frustum_volume'] = (np.pi * result['frustum_height'] / 3.0) * (
            result['pile_radius']**2 +
            result['pile_radius'] * result['frustum_top_radius'] +
            result['frustum_top_radius']**2
        )
        result['soil_cylinder_volume'] = np.pi * result['frustum_top_radius']**2 * result['soil_cylinder_height']
        
        # Note: frustum_base_radius is now calculated as frustum_top_radius above
        # This is kept for backward compatibility but should use frustum_top_radius
        result['frustum_base_radius'] = result['frustum_top_radius']
        
        logger.info("Pile properties calculated successfully")
        return result
        
    def filter_piles_by_boundary(self, data: pd.DataFrame, 
                                boundary_coords: List[Tuple[float, float]]) -> pd.DataFrame:
        """
        Filter piles to only include those within the site boundary.
        
        Args:
            data: Pile DataFrame with coordinates
            boundary_coords: List of (x, y) coordinates defining site boundary
            
        Returns:
            Filtered DataFrame containing only piles within boundary
        """
        from shapely.geometry import Point, Polygon
        
        # Create boundary polygon
        boundary_polygon = Polygon(boundary_coords)
        
        # Check which piles are within boundary
        pile_points = [Point(x, y) for x, y in zip(data['x_coord'], data['y_coord'])]
        within_boundary = [boundary_polygon.contains(point) for point in pile_points]
        
        filtered_data = data[within_boundary].copy()
        
        excluded_count = len(data) - len(filtered_data)
        if excluded_count > 0:
            logger.info(f"Excluded {excluded_count} piles outside site boundary")
            
        return filtered_data
